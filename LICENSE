MIT License

Copyright (c) 2024 eQosystem

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

================================================================================

eQosystem Landing Page - Additional License Information

This project is part of the eQosystem initiative, which aims to bring minds 
together to drive a major scientific and technological transformation in the 
quantum computing domain.

The eQosystem project focuses on three key areas:
- Quantum Foundations: Learning and exploring quantum computing principles
- Quantum Software: Developing quantum algorithms and applications  
- Quantum Software AI: Bridging artificial intelligence and quantum computing

For more information about the eQosystem initiative, please visit:
https://eqosystem.com

Contact Information:
- Email: <EMAIL>
- Website: https://eqosystem.com

================================================================================

Third-Party Licenses:

This project uses several open-source libraries and frameworks. Please refer
to their respective licenses:

- FastAPI: MIT License
- Vue.js: MIT License  
- Tailwind CSS: MIT License
- SQLAlchemy: MIT License
- Uvicorn: BSD License
- Pydantic: MIT License

All third-party dependencies maintain their original licenses and copyright
notices as specified in their respective packages.

================================================================================
