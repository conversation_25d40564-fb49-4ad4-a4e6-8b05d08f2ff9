# =============================================================================
# eQosystem Landing Page - Docker Compose Configuration
# =============================================================================
#
# This Docker Compose file provides a complete development and production
# environment for the eQosystem landing page application.
#
# Usage:
#   Development:  docker-compose up --build
#   Production:   docker-compose -f docker-compose.yml up -d
#   Stop:         docker-compose down
#   Logs:         docker-compose logs -f
#
# =============================================================================

version: '3.8'

services:
  # ==========================================================================
  # eQosystem Landing Page Application
  # ==========================================================================
  eqosystem:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VERSION: ${VERSION:-1.0.0}
        VCS_REF: ${VCS_REF:-}
    
    image: eqosystem:1.0.0
    container_name: eqosystem-app
    
    # Port mapping
    ports:
      - "8002:8002"
    
    # Environment variables
    environment:
      - ENV=production
      - PORT=8002
      - HOST=0.0.0.0
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # Volume mounts for data persistence
    volumes:
      - eqosystem_data:/app/data
      - eqosystem_db:/app/backend
    
    # Restart policy
    restart: unless-stopped
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
    
    # Labels for organization
    labels:
      - "app=eqosystem"
      - "version=1.0.0"
      - "environment=production"
      - "maintainer=eQosystem Team <<EMAIL>>"
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# =============================================================================
# Named Volumes for Data Persistence
# =============================================================================
volumes:
  # Application data volume
  eqosystem_data:
    driver: local
    labels:
      - "app=eqosystem"
      - "type=data"
  
  # Database volume
  eqosystem_db:
    driver: local
    labels:
      - "app=eqosystem"
      - "type=database"

# =============================================================================
# Networks (optional, using default bridge network)
# =============================================================================
networks:
  default:
    name: eqosystem-network
    labels:
      - "app=eqosystem"
      - "environment=production"

# =============================================================================
# Configuration Notes
# =============================================================================
#
# Environment Variables:
#   BUILD_DATE - Build timestamp (auto-generated)
#   VERSION    - Application version (default: 1.0.0)
#   VCS_REF    - Git commit hash (auto-generated)
#
# Volumes:
#   eqosystem_data - Application data persistence
#   eqosystem_db   - SQLite database persistence
#
# Ports:
#   8002 - Main application port (HTTP)
#
# Health Check:
#   Endpoint: /api/health
#   Interval: 30 seconds
#   Timeout:  10 seconds
#   Retries:  3 attempts
#
# Resource Limits:
#   CPU:    1.0 cores max, 0.25 cores reserved
#   Memory: 512MB max, 128MB reserved
#
# =============================================================================
