#!/usr/bin/env python3
"""
Test script for the eQosystem API
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_subscribe():
    """Test the subscription endpoint"""
    print("Testing subscription endpoint...")
    
    # Test valid email
    response = requests.post(
        f"{BASE_URL}/api/subscribe",
        json={"email": "<EMAIL>"},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    
    # Test duplicate email
    response = requests.post(
        f"{BASE_URL}/api/subscribe",
        json={"email": "<EMAIL>"},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Duplicate Status: {response.status_code}")
    print(f"Duplicate Response: {response.json()}")

def test_get_subscribers():
    """Test the get subscribers endpoint"""
    print("\nTesting get subscribers endpoint...")
    
    response = requests.get(f"{BASE_URL}/api/subscribers")
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")

if __name__ == "__main__":
    test_subscribe()
    test_get_subscribers()
