"""
eQosystem Database Configuration

This module handles all database-related operations including:
- SQLite database setup with SQLAlchemy
- Database models definition
- Session management
- Database initialization

Author: eQosystem Team
Version: 2.0.0
"""

from datetime import datetime, timezone
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from typing import Generator

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQLite database URL - stores data in subscribers.db file
DATABASE_URL = "sqlite:///./subscribers.db"

# Create SQLAlchemy engine with SQLite optimizations
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},  # Allow multiple threads
    echo=False  # Set to True for SQL query logging in development
)

# Create session factory for database operations
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Create declarative base for ORM models
Base = declarative_base()


# =============================================================================
# DATABASE MODELS
# =============================================================================

class Subscriber(Base):
    """
    Database model for email subscribers.
    
    This model represents users who have subscribed to the eQosystem newsletter.
    Each subscriber has a unique email address and a timestamp of when they subscribed.
    
    Attributes:
        id (int): Primary key, auto-incrementing
        email (str): Unique email address, indexed for fast lookups
        subscribed_at (datetime): UTC timestamp when subscription was created
    """
    __tablename__ = "subscribers"

    # Primary key with auto-increment
    id = Column(Integer, primary_key=True, index=True)
    
    # Email field with unique constraint and index for performance
    email = Column(String, unique=True, index=True, nullable=False)
    
    # Timestamp field with UTC timezone
    subscribed_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )

    def __repr__(self) -> str:
        """String representation of the Subscriber object."""
        return f"<Subscriber(id={self.id}, email='{self.email}')>"

    def to_dict(self) -> dict:
        """Convert subscriber to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "email": self.email,
            "subscribed_at": self.subscribed_at.isoformat()
        }


# =============================================================================
# DATABASE UTILITIES
# =============================================================================

def get_database_session() -> Generator:
    """
    Database dependency for FastAPI dependency injection.
    
    Creates a new database session for each request and ensures
    proper cleanup after the request is completed.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database() -> None:
    """
    Initialize the database by creating all tables.
    
    This function should be called when the application starts
    to ensure all database tables exist.
    """
    Base.metadata.create_all(bind=engine)


def get_subscriber_count() -> int:
    """
    Get the total number of subscribers in the database.
    
    Returns:
        int: Total number of subscribers
    """
    db = SessionLocal()
    try:
        return db.query(Subscriber).count()
    finally:
        db.close()
