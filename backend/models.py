"""
eQosystem API Models

This module defines Pydantic models for request/response validation.
These models ensure data integrity and provide automatic API documentation.

Author: eQosystem Team
Version: 2.0.0
"""

from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional



# =============================================================================
# REQUEST MODELS
# =============================================================================

class SubscribeRequest(BaseModel):
    """
    Request model for email subscription.
    
    Validates that the provided email address is in correct format
    and meets the requirements for newsletter subscription.
    
    Attributes:
        email: Valid email address (automatically validated by EmailStr)
    """
    email: EmailStr = Field(
        ...,
        description="Valid email address for newsletter subscription",
        example="<EMAIL>"
    )

    class Config:
        """Pydantic configuration for the model."""
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


# =============================================================================
# RESPONSE MODELS
# =============================================================================

class SubscribeResponse(BaseModel):
    """
    Response model for successful email subscription.
    
    Attributes:
        message: Success message confirming subscription
        email: The email address that was subscribed
        id: Database ID of the new subscriber
    """
    message: str = Field(
        description="Success message",
        example="Successfully subscribed to eQosystem newsletter"
    )
    email: str = Field(
        description="Subscribed email address",
        example="<EMAIL>"
    )
    id: int = Field(
        description="Subscriber database ID",
        example=1
    )


class SubscriberInfo(BaseModel):
    """
    Model representing a single subscriber's information.
    
    Attributes:
        id: Database ID of the subscriber
        email: Email address
        subscribed_at: ISO formatted timestamp of subscription
    """
    id: int = Field(description="Subscriber ID", example=1)
    email: str = Field(description="Email address", example="<EMAIL>")
    subscribed_at: str = Field(
        description="Subscription timestamp (ISO format)",
        example="2024-06-04T10:30:00.000Z"
    )


class SubscribersListResponse(BaseModel):
    """
    Response model for the subscribers list endpoint.
    
    Attributes:
        count: Total number of subscribers
        subscribers: List of subscriber information
    """
    count: int = Field(
        description="Total number of subscribers",
        example=42
    )
    subscribers: List[SubscriberInfo] = Field(
        description="List of all subscribers",
        example=[
            {
                "id": 1,
                "email": "<EMAIL>",
                "subscribed_at": "2024-06-04T10:30:00.000Z"
            },
            {
                "id": 2,
                "email": "<EMAIL>",
                "subscribed_at": "2024-06-04T11:15:00.000Z"
            }
        ]
    )


class HealthResponse(BaseModel):
    """
    Response model for health check endpoint.
    
    Attributes:
        status: Application health status
        version: Application version
        service: Service name
        database: Database connection status
        subscribers_count: Current number of subscribers
    """
    status: str = Field(description="Health status", example="healthy")
    version: str = Field(description="Application version", example="2.0.0")
    service: str = Field(description="Service name", example="eQosystem API")
    database: str = Field(description="Database status", example="connected")
    subscribers_count: int = Field(description="Total subscribers", example=42)


class ErrorResponse(BaseModel):
    """
    Response model for error cases.
    
    Attributes:
        error: Error type or category
        message: Human-readable error message
        details: Optional additional error details
    """
    error: str = Field(description="Error type", example="validation_error")
    message: str = Field(
        description="Error message",
        example="Email address is already subscribed"
    )
    details: Optional[str] = Field(
        default=None,
        description="Additional error details",
        example="The <NAME_EMAIL> is already in our database"
    )
