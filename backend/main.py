"""FastAPI backend for the eQosystem landing page.

This backend exposes a small API used by the Vue.js frontend to submit
email subscriptions.  Subscribers are stored in memory for simplicity.

The application also serves the static frontend contained in ``../frontend``.
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, EmailStr
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from typing import List

# FastAPI application instance
app = FastAPI()

# In-memory store for subscriber email addresses
subscribers: List[str] = []

# Allow the demo frontend to request the API from any origin.  This is only
# for demonstration purposes and should be restricted in production.
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_methods=['*'],
    allow_headers=['*'],
)

class SubscribeRequest(BaseModel):
    """Schema for the subscription request payload."""

    email: EmailStr

@app.post('/api/subscribe')
def subscribe(req: SubscribeRequest):
    """Add a new subscriber if the email is not already present."""

    if req.email in subscribers:
        raise HTTPException(status_code=400, detail='Email already subscribed')

    subscribers.append(req.email)
    return {'message': 'Subscribed successfully'}


# Serve the static Vue frontend.  ``html=True`` allows index.html to be
# returned for unknown routes so the Vue app works correctly when refreshing.
app.mount(
    '/',
    StaticFiles(directory='frontend', html=True),
    name='frontend'
)
