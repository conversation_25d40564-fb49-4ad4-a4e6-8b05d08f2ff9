"""
eQosystem Landing Page - FastAPI Backend

This module provides the backend API for the eQosystem landing page.
It handles email subscriptions with persistent SQLite storage and serves
the Vue.js frontend application.

Features:
- Email subscription API with validation
- SQLite database for persistent storage
- Admin endpoint to view subscribers
- Static file serving for frontend
- CORS middleware for development

Author: eQosystem Team
Version: 2.0.0
"""

from datetime import datetime, timezone
from typing import List, Generator

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, EmailStr
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQLite database configuration
DATABASE_URL = "sqlite:///./subscribers.db"

# Create database engine with SQLite-specific settings
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False}  # Allow multiple threads for SQLite
)

# Create session factory for database operations
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create declarative base for ORM models
Base = declarative_base()


# =============================================================================
# DATABASE MODELS
# =============================================================================

class Subscriber(Base):
    """
    Database model for email subscribers.

    Attributes:
        id: Primary key, auto-incrementing integer
        email: Unique email address, indexed for fast lookups
        subscribed_at: Timestamp when subscription was created (UTC)
    """
    __tablename__ = "subscribers"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    subscribed_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )

    def __repr__(self):
        return f"<Subscriber(email='{self.email}', subscribed_at='{self.subscribed_at}')>"


# Create all database tables
Base.metadata.create_all(bind=engine)

# =============================================================================
# FASTAPI APPLICATION SETUP
# =============================================================================

# Create FastAPI application instance with metadata
app = FastAPI(
    title="eQosystem API",
    description="Backend API for eQosystem landing page with email subscriptions",
    version="2.0.0",
    docs_url="/api/docs",  # Swagger UI at /api/docs
    redoc_url="/api/redoc"  # ReDoc at /api/redoc
)

# Add CORS middleware for cross-origin requests
# NOTE: In production, replace '*' with specific allowed origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["GET", "POST"],  # Only allow necessary methods
    allow_headers=["*"],
)


# =============================================================================
# DEPENDENCY INJECTION
# =============================================================================

def get_db() -> Generator[Session, None, None]:
    """
    Database dependency injection.

    Creates a database session for each request and ensures proper cleanup.

    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# =============================================================================
# PYDANTIC MODELS (REQUEST/RESPONSE SCHEMAS)
# =============================================================================

class SubscribeRequest(BaseModel):
    """
    Request schema for email subscription.

    Attributes:
        email: Valid email address (validated by EmailStr)
    """
    email: EmailStr

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class SubscribeResponse(BaseModel):
    """
    Response schema for successful subscription.

    Attributes:
        message: Success message
        email: Subscribed email address
    """
    message: str
    email: str


class SubscribersResponse(BaseModel):
    """
    Response schema for subscribers list.

    Attributes:
        count: Total number of subscribers
        subscribers: List of subscriber data
    """
    count: int
    subscribers: List[dict]

# =============================================================================
# API ENDPOINTS
# =============================================================================

@app.post('/api/subscribe', response_model=SubscribeResponse)
def subscribe(req: SubscribeRequest, db: Session = Depends(get_db)):
    """
    Subscribe a new email address to the eQosystem newsletter.

    Args:
        req: Request containing email address
        db: Database session (injected dependency)

    Returns:
        SubscribeResponse: Success message with email

    Raises:
        HTTPException: 400 if email already exists
    """
    # Check if email already exists in database
    existing_subscriber = db.query(Subscriber).filter(
        Subscriber.email == req.email
    ).first()

    if existing_subscriber:
        raise HTTPException(
            status_code=400,
            detail='Email already subscribed'
        )

    # Create new subscriber record
    new_subscriber = Subscriber(email=req.email)
    db.add(new_subscriber)
    db.commit()
    db.refresh(new_subscriber)

    return SubscribeResponse(
        message='Subscribed successfully',
        email=req.email
    )


@app.get('/api/subscribers', response_model=SubscribersResponse)
def get_subscribers(db: Session = Depends(get_db)):
    """
    Get all subscribers (admin endpoint).

    Args:
        db: Database session (injected dependency)

    Returns:
        SubscribersResponse: Count and list of all subscribers
    """
    subscribers = db.query(Subscriber).all()

    return SubscribersResponse(
        count=len(subscribers),
        subscribers=[
            {
                'id': s.id,
                'email': s.email,
                'subscribed_at': s.subscribed_at.isoformat()
            }
            for s in subscribers
        ]
    )


@app.get('/api/health')
def health_check():
    """
    Health check endpoint for monitoring.

    Returns:
        dict: Application status and version
    """
    return {
        'status': 'healthy',
        'version': '2.0.0',
        'service': 'eQosystem API'
    }


# =============================================================================
# STATIC FILE SERVING
# =============================================================================

# Mount static files for Vue.js frontend
# The html=True parameter enables SPA routing by serving index.html for unknown routes
import os
frontend_dir = '../frontend' if os.path.exists('../frontend') else 'frontend'
app.mount(
    '/',
    StaticFiles(directory=frontend_dir, html=True),
    name='frontend'
)


# =============================================================================
# APPLICATION STARTUP
# =============================================================================

if __name__ == "__main__":
    import uvicorn

    # Run the application with uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
