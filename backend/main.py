"""FastAPI backend for the eQosystem landing page.

This backend exposes a small API used by the Vue.js frontend to submit
email subscriptions. Subscribers are stored in a SQLite database for persistence.

The application also serves the static frontend contained in ``../frontend``.
"""

from datetime import datetime, timezone
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, EmailStr
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import List

# Database setup
DATABASE_URL = "sqlite:///./subscribers.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database model
class Subscriber(Base):
    __tablename__ = "subscribers"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    subscribed_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

# Create tables
Base.metadata.create_all(bind=engine)

# FastAPI application instance
app = FastAPI(title="eQosystem API", description="API for eQosystem landing page")

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Allow the demo frontend to request the API from any origin.  This is only
# for demonstration purposes and should be restricted in production.
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_methods=['*'],
    allow_headers=['*'],
)

class SubscribeRequest(BaseModel):
    """Schema for the subscription request payload."""

    email: EmailStr

@app.post('/api/subscribe')
def subscribe(req: SubscribeRequest, db: Session = Depends(get_db)):
    """Add a new subscriber if the email is not already present."""

    # Check if email already exists
    existing_subscriber = db.query(Subscriber).filter(Subscriber.email == req.email).first()
    if existing_subscriber:
        raise HTTPException(status_code=400, detail='Email already subscribed')

    # Create new subscriber
    new_subscriber = Subscriber(email=req.email)
    db.add(new_subscriber)
    db.commit()
    db.refresh(new_subscriber)

    return {'message': 'Subscribed successfully'}

@app.get('/api/subscribers')
def get_subscribers(db: Session = Depends(get_db)):
    """Get all subscribers (for admin purposes)."""
    subscribers = db.query(Subscriber).all()
    return {
        'count': len(subscribers),
        'subscribers': [{'email': s.email, 'subscribed_at': s.subscribed_at} for s in subscribers]
    }


# Serve the static Vue frontend.  ``html=True`` allows index.html to be
# returned for unknown routes so the Vue app works correctly when refreshing.
app.mount(
    '/',
    StaticFiles(directory='../frontend', html=True),
    name='frontend'
)
