"""
eQosystem Landing Page - FastAPI Backend

Modern, production-ready backend for the eQosystem landing page.
Features email subscription management with SQLite persistence,
comprehensive API documentation, and robust error handling.

Key Features:
- Email subscription API with validation
- SQLite database with SQLAlchemy ORM
- Automatic API documentation with Swagger UI
- Health monitoring endpoint
- CORS support for frontend integration
- Static file serving for Vue.js frontend

Author: eQosystem Team
Version: 2.0.0
License: MIT
"""

import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

# Import our custom modules
from database import get_database_session, init_database, Subscriber, get_subscriber_count
from models import (
    SubscribeRequest, SubscribeResponse, SubscribersListResponse,
    HealthResponse, ErrorResponse, SubscriberInfo
)


# =============================================================================
# APPLICATION LIFECYCLE
# =============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    
    Handles startup and shutdown events for the FastAPI application.
    Initializes the database on startup.
    """
    # Startup: Initialize database
    print("🚀 Starting eQosystem API...")
    init_database()
    print("✅ Database initialized")
    
    yield
    
    # Shutdown: Cleanup if needed
    print("🛑 Shutting down eQosystem API...")


# =============================================================================
# FASTAPI APPLICATION SETUP
# =============================================================================

# Create FastAPI application with comprehensive metadata
app = FastAPI(
    title="eQosystem API",
    description="""
    **eQosystem Landing Page Backend API**
    
    This API powers the eQosystem landing page, providing email subscription
    management and serving the Vue.js frontend application.
    
    ## Features
    
    * **Email Subscriptions**: Manage newsletter subscriptions with validation
    * **Data Persistence**: SQLite database with automatic backups
    * **Admin Interface**: View and manage subscribers
    * **Health Monitoring**: Real-time application status
    * **Modern Architecture**: Built with FastAPI and SQLAlchemy
    
    ## Quantum Domains
    
    eQosystem focuses on three key areas:
    - **Quantum Foundations**: Learning and exploring quantum computing
    - **Quantum Software**: Developing quantum algorithms and applications  
    - **Quantum Software AI**: Bridging AI and quantum computing
    """,
    version="2.0.0",
    contact={
        "name": "eQosystem Team",
        "url": "https://eqosystem.com",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan
)

# Configure CORS for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)


# =============================================================================
# API ENDPOINTS
# =============================================================================

@app.post(
    "/api/subscribe",
    response_model=SubscribeResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Subscribe to Newsletter",
    description="Subscribe an email address to the eQosystem newsletter",
    responses={
        201: {"description": "Successfully subscribed"},
        400: {"model": ErrorResponse, "description": "Email already exists or invalid"},
        422: {"description": "Validation error"}
    }
)
async def subscribe_email(
    request: SubscribeRequest,
    db: Session = Depends(get_database_session)
) -> SubscribeResponse:
    """
    Subscribe a new email address to the eQosystem newsletter.
    
    This endpoint validates the email format and ensures uniqueness
    before adding it to the subscriber database.
    
    Args:
        request: Email subscription request containing the email address
        db: Database session (automatically injected)
        
    Returns:
        SubscribeResponse: Confirmation with subscriber details
        
    Raises:
        HTTPException: 400 if email already exists or is invalid
    """
    try:
        # Create new subscriber record
        new_subscriber = Subscriber(email=request.email)
        db.add(new_subscriber)
        db.commit()
        db.refresh(new_subscriber)
        
        return SubscribeResponse(
            message="Successfully subscribed to eQosystem newsletter",
            email=request.email,
            id=new_subscriber.id
        )
        
    except IntegrityError:
        # Email already exists (unique constraint violation)
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email address is already subscribed to our newsletter"
        )
    except Exception as e:
        # Unexpected error
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred. Please try again later."
        )


@app.get(
    "/api/subscribers",
    response_model=SubscribersListResponse,
    summary="List All Subscribers",
    description="Get a list of all newsletter subscribers (admin endpoint)",
    responses={
        200: {"description": "List of subscribers"},
        500: {"description": "Database error"}
    }
)
async def get_subscribers(
    db: Session = Depends(get_database_session)
) -> SubscribersListResponse:
    """
    Retrieve all newsletter subscribers.
    
    This is an administrative endpoint that returns all subscribers
    in the database, ordered by subscription date (newest first).
    
    Args:
        db: Database session (automatically injected)
        
    Returns:
        SubscribersListResponse: Count and list of all subscribers
    """
    try:
        # Get all subscribers ordered by subscription date
        subscribers = db.query(Subscriber).order_by(
            Subscriber.subscribed_at.desc()
        ).all()
        
        # Convert to response format
        subscriber_list = [
            SubscriberInfo(
                id=sub.id,
                email=sub.email,
                subscribed_at=sub.subscribed_at.isoformat()
            )
            for sub in subscribers
        ]
        
        return SubscribersListResponse(
            count=len(subscribers),
            subscribers=subscriber_list
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Unable to retrieve subscribers. Please try again later."
        )


@app.get(
    "/api/health",
    response_model=HealthResponse,
    summary="Health Check",
    description="Check the health status of the API and database",
    responses={
        200: {"description": "Service is healthy"},
        503: {"description": "Service is unhealthy"}
    }
)
async def health_check() -> HealthResponse:
    """
    Health check endpoint for monitoring and load balancers.
    
    Returns the current status of the application including
    database connectivity and basic metrics.
    
    Returns:
        HealthResponse: Application health status and metrics
    """
    try:
        # Test database connectivity
        subscriber_count = get_subscriber_count()
        
        return HealthResponse(
            status="healthy",
            version="2.0.0",
            service="eQosystem API",
            database="connected",
            subscribers_count=subscriber_count
        )
        
    except Exception as e:
        # Database connection failed
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "version": "2.0.0",
                "service": "eQosystem API",
                "database": "disconnected",
                "error": str(e)
            }
        )


# =============================================================================
# STATIC FILE SERVING
# =============================================================================

# Determine frontend directory path (works in both dev and production)
frontend_path = "../frontend" if os.path.exists("../frontend") else "frontend"

# Mount static files for Vue.js frontend
# html=True enables SPA routing by serving index.html for unknown routes
app.mount(
    "/",
    StaticFiles(directory=frontend_path, html=True),
    name="frontend"
)


# =============================================================================
# APPLICATION ENTRY POINT
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    # Run the application with uvicorn
    print("🌟 Starting eQosystem Landing Page")
    print("📡 API Documentation: http://localhost:8002/api/docs")
    print("🎨 Frontend: http://localhost:8002")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )
