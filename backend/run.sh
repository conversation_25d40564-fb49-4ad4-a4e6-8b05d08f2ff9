#!/bin/sh
# =============================================================================
# eQosystem Landing Page - Production Startup Script
# =============================================================================
#
# This script starts the FastAPI application with production-ready settings.
# It supports environment variable configuration for flexible deployment.
#
# Environment Variables:
#   PORT: Server port (default: 8001)
#   HOST: Server host (default: 0.0.0.0)
#   WORKERS: Number of worker processes (default: 1)
#   LOG_LEVEL: Logging level (default: info)
#
# Usage:
#   ./run.sh                    # Start with defaults
#   PORT=8080 ./run.sh         # Start on port 8080
#   WORKERS=4 ./run.sh         # Start with 4 workers
#
# =============================================================================

# Set default values
DEFAULT_PORT=8001
DEFAULT_HOST="0.0.0.0"
DEFAULT_WORKERS=1
DEFAULT_LOG_LEVEL="info"

# Use environment variables or defaults
PORT=${PORT:-$DEFAULT_PORT}
HOST=${HOST:-$DEFAULT_HOST}
WORKERS=${WORKERS:-$DEFAULT_WORKERS}
LOG_LEVEL=${LOG_LEVEL:-$DEFAULT_LOG_LEVEL}

# Print startup information
echo "🚀 Starting eQosystem API Server..."
echo "   Host: $HOST"
echo "   Port: $PORT"
echo "   Workers: $WORKERS"
echo "   Log Level: $LOG_LEVEL"
echo "   Database: SQLite (subscribers.db)"
echo ""

# Start the server
exec uvicorn backend.main:app \
    --host "$HOST" \
    --port "$PORT" \
    --workers "$WORKERS" \
    --log-level "$LOG_LEVEL" \
    --access-log
