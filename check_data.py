#!/usr/bin/env python3
"""
eQosystem Data Checker

This script helps you inspect the SQLite database and verify data storage.
It can be used both locally and inside Docker containers.

Usage:
    python3 check_data.py                    # Check local database
    python3 check_data.py --docker           # Check database in Docker container
    python3 check_data.py --api              # Check via API
"""

import sqlite3
import json
import sys
import os
from datetime import datetime

def check_local_database():
    """Check the local SQLite database."""
    db_path = "backend/subscribers.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table info
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 Tables found: {[table[0] for table in tables]}")
        
        # Get subscribers
        cursor.execute("SELECT * FROM subscribers ORDER BY subscribed_at DESC;")
        subscribers = cursor.fetchall()
        
        print(f"\n👥 Total subscribers: {len(subscribers)}")
        print("=" * 60)
        
        if subscribers:
            print(f"{'ID':<5} {'Email':<30} {'Subscribed At':<25}")
            print("-" * 60)
            for sub in subscribers:
                print(f"{sub[0]:<5} {sub[1]:<30} {sub[2]:<25}")
        else:
            print("No subscribers found.")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error accessing database: {e}")
        return False

def check_via_api():
    """Check data via API endpoint."""
    import urllib.request
    import urllib.error
    
    try:
        with urllib.request.urlopen('http://localhost:8001/api/subscribers') as response:
            data = json.loads(response.read().decode())
            
        print(f"👥 Total subscribers: {data['count']}")
        print("=" * 60)
        
        if data['subscribers']:
            print(f"{'ID':<5} {'Email':<30} {'Subscribed At':<25}")
            print("-" * 60)
            for sub in data['subscribers']:
                print(f"{sub['id']:<5} {sub['email']:<30} {sub['subscribed_at']:<25}")
        else:
            print("No subscribers found.")
            
        return True
        
    except urllib.error.URLError as e:
        print(f"❌ Error connecting to API: {e}")
        print("Make sure the application is running on http://localhost:8001")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_docker_container():
    """Check database inside Docker container."""
    import subprocess
    
    try:
        # Check if container is running
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=eqosystem-app", "--format", "{{.Names}}"],
            capture_output=True, text=True, check=True
        )
        
        if "eqosystem-app" not in result.stdout:
            print("❌ Docker container 'eqosystem-app' is not running")
            print("Start it with: docker run --name eqosystem-app -p 8001:8001 -d eqosystem-landing:2.0.0")
            return False
        
        print("✅ Docker container is running")
        
        # Copy database from container
        subprocess.run(
            ["docker", "cp", "eqosystem-app:/app/backend/subscribers.db", "/tmp/docker_subscribers.db"],
            check=True
        )
        
        # Check the copied database
        conn = sqlite3.connect("/tmp/docker_subscribers.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM subscribers ORDER BY subscribed_at DESC;")
        subscribers = cursor.fetchall()
        
        print(f"\n👥 Total subscribers in Docker: {len(subscribers)}")
        print("=" * 60)
        
        if subscribers:
            print(f"{'ID':<5} {'Email':<30} {'Subscribed At':<25}")
            print("-" * 60)
            for sub in subscribers:
                print(f"{sub[0]:<5} {sub[1]:<30} {sub[2]:<25}")
        else:
            print("No subscribers found.")
        
        conn.close()
        os.remove("/tmp/docker_subscribers.db")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker command failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🔍 eQosystem Data Checker")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--docker":
            success = check_docker_container()
        elif sys.argv[1] == "--api":
            success = check_via_api()
        else:
            print("Usage: python3 check_data.py [--docker|--api]")
            sys.exit(1)
    else:
        success = check_local_database()
    
    if success:
        print("\n✅ Data check completed successfully!")
    else:
        print("\n❌ Data check failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
