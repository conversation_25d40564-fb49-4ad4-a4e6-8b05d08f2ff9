# =============================================================================
# eQosystem Landing Page - Docker Ignore Configuration
# =============================================================================

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
CONTRIBUTING.md
SECURITY.md
LICENSE
docs/
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Database files (will be created in container)
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
.tmp/
temp/
tmp/

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Deployment scripts
deploy.sh
*.sh

# Development tools
.pre-commit-config.yaml
.flake8
.pylintrc
mypy.ini
pytest.ini
tox.ini

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Security
.env.local
.env.production
.env.staging
secrets/
*.key
*.pem

# Project specific development files
manage_data.py
tests/
test_*.py
*_test.py
