# =============================================================================
# eQosystem Landing Page - Python Dependencies
# =============================================================================
# 
# Production dependencies for the eQosystem landing page application.
# This file contains all required packages with pinned versions for
# reproducible builds and security.
#
# To install: pip install -r requirements.txt
# To update: pip install -r requirements.txt --upgrade
#
# =============================================================================

# Web Framework
fastapi==0.104.1                # Modern, fast web framework for building APIs
uvicorn[standard]==0.24.0       # ASGI server implementation

# Database
sqlalchemy==2.0.23              # SQL toolkit and Object-Relational Mapping
aiosqlite==0.19.0               # Async SQLite database adapter

# Validation and Serialization
email-validator==2.1.0          # Email address validation
pydantic==2.5.0                 # Data validation using Python type hints

# Additional Dependencies
python-multipart==0.0.6         # Form data parsing support
jinja2==3.1.2                   # Template engine (for error pages)

# Development Dependencies (optional, for local development)
# Uncomment the following lines for development:
# pytest==7.4.3                 # Testing framework
# pytest-asyncio==0.21.1        # Async testing support
# black==23.11.0                 # Code formatter
# flake8==6.1.0                 # Code linter
# mypy==1.7.1                   # Static type checker

# =============================================================================
# Version Information
# =============================================================================
# Python: >=3.9
# Last updated: 2024-06-04
# Compatible with: Docker, Kubernetes, Cloud platforms
# =============================================================================
