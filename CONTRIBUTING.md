# Contributing

Thank you for considering contributing to this project.

1. Fork the repository and create your branch from `main`.
2. Ensure any install or build dependencies are removed before the end of the layer when doing a build.
3. Update the README with details of changes to the interface, this includes new environment variables, exposed ports, useful file locations and container parameters.
4. Increase the version numbers in any examples files and the README to the new version that this Pull Request would represent.
5. You may merge the Pull Request in once you have the sign-off from a maintainer.
