# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2024-06-04

### Added
- **Modern Design System**: Complete redesign with elegant dark theme
- **Particle Animation**: Interactive background with connected particles using Canvas API
- **Toast Notifications**: Beautiful success/error notifications with animations
- **SQLite Database**: Persistent data storage replacing in-memory storage
- **Admin Endpoint**: `/api/subscribers` to view all subscribers with timestamps
- **Tailwind CSS**: Modern utility-first CSS framework integration
- **Gradient Effects**: Beautiful color transitions and hover states
- **Responsive Design**: Optimized layout for all screen sizes
- **Vue.js 3 Composition API**: Modern reactive components
- **Email Validation**: Enhanced client and server-side validation
- **Loading States**: Spinner and disabled states during form submission
- **Quantum Theme**: Three feature cards representing quantum domains
- **Custom Animations**: Fade-in, slide-up, and zoom effects
- **SVG Assets**: Custom favicon and background graphics
- **API Testing**: Comprehensive test script for endpoints

### Changed
- **Database**: Migrated from in-memory storage to SQLite with SQLAlchemy
- **UI/UX**: Complete redesign with modern aesthetics and animations
- **Typography**: Improved font hierarchy with gradient text effects
- **Color Palette**: Quantum-inspired gradient colors (orange, purple, blue, green)
- **Form Design**: Enhanced input styling with backdrop blur effects
- **Button Design**: Modern button with arrow icon and hover animations
- **Error Handling**: Improved error messages and user feedback
- **API Response**: Enhanced JSON responses with detailed error information
- **Port Configuration**: Changed default port from 8000 to 8001

### Enhanced
- **Performance**: Optimized particle animation with requestAnimationFrame
- **Accessibility**: Better semantic HTML and ARIA attributes
- **Code Organization**: Modular Vue.js components and clean separation
- **Documentation**: Comprehensive README with setup and feature descriptions
- **Development Experience**: Hot reload and better error handling

### Technical Improvements
- **Database Schema**: Proper timestamp handling with UTC timezone
- **CORS Configuration**: Enhanced cross-origin resource sharing
- **Static File Serving**: Improved path resolution for assets
- **Error Handling**: Robust exception handling and user feedback
- **Code Quality**: Type hints and proper Python conventions

## [1.0.0] - 2025-06-04
### Added
- Initial release with Vue landing page and FastAPI backend
- Basic email subscription functionality
- In-memory storage for demo purposes
