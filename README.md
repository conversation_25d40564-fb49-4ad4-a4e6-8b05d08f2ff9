# eQosystem Landing Page

This project provides a minimal landing page implemented with **Vue.js** and a
small **FastAPI** backend. Visitors can submit their email address which is kept
in memory for demo purposes.

## Project layout

- **frontend/** – static files served by FastAPI.
- **backend/** – FastAPI application exposing `/api/subscribe` and serving the
  frontend files.

## Requirements

- Python 3.12+
- `pip` for installing dependencies

## Local development

1. Install dependencies:
   ```bash
   pip install -r backend/requirements.txt
   ```
2. Copy `.env.example` to `.env` and adjust if necessary.
3. Start the server:
   ```bash
   ./backend/run.sh
   ```
4. Visit [http://localhost:8000](http://localhost:8000) in your browser.

## Docker

A `Dockerfile` is provided for containerised deployments.

Build and run the container:
```bash
docker build -t eqosystem:latest .
docker run -p 8000:8000 eqosystem:latest
```

The container honours the `PORT` environment variable.

## Deployment

The application is fully static on the frontend. The backend only keeps
subscribers in memory, so it is suitable for demonstrations only.

## License

See [LICENSE](LICENSE) for license information.
