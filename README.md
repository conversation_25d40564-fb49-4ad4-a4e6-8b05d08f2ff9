# eQosystem Landing Page v2.0

A modern, elegant landing page for eQosystem built with Vue.js and FastAPI, featuring a beautiful design with animations, particle effects, and persistent data storage.

## ✨ Features

### Frontend
- **Modern Design**: Elegant dark theme with gradient text and hover effects
- **Responsive Layout**: Optimized for all screen sizes
- **Particle Animation**: Interactive background with connected particles
- **Toast Notifications**: Beautiful success/error notifications
- **Smooth Animations**: CSS animations with Tailwind CSS
- **Vue.js 3**: Reactive components with Composition API

### Backend
- **FastAPI**: High-performance Python web framework
- **SQLite Database**: Persistent data storage for subscribers
- **Email Validation**: Server-side email validation
- **CORS Support**: Cross-origin resource sharing enabled
- **Admin Endpoint**: View all subscribers with timestamps

### Design Elements
- **Quantum Theme**: Three feature cards representing quantum domains
- **Gradient Effects**: Beautiful color transitions and hover states
- **Typography**: Carefully crafted text hierarchy
- **Interactive Elements**: Hover effects on cards and buttons

## Project layout

- **frontend/** – Vue.js application with modern design and animations
- **backend/** – FastAPI application with SQLite database for persistent storage

## Requirements

- Python 3.12+
- `pip` for installing dependencies

## 🚀 Local development

1. **Install dependencies:**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Start the server:**
   ```bash
   cd backend
   python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8001
   ```

3. **Open your browser to:** `http://localhost:8001`

## 📡 API Endpoints

- `POST /api/subscribe` - Subscribe with an email address
- `GET /api/subscribers` - Get all subscribers (admin endpoint)
- Static files served from `/` (Vue.js frontend)

## 🧪 Testing

Run the API tests:
```bash
python3 test_api.py
```

## Docker

A `Dockerfile` is provided for containerised deployments.

Build and run the container:
```bash
docker build -t eqosystem:latest .
docker run -p 8000:8000 eqosystem:latest
```

The container honours the `PORT` environment variable.

## Deployment

The application is fully static on the frontend. The backend only keeps
subscribers in memory, so it is suitable for demonstrations only.

## License

See [LICENSE](LICENSE) for license information.
