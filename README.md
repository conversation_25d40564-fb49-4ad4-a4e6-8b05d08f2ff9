# eQosystem Landing Page v2.0

A modern, elegant landing page for eQosystem built with Vue.js and FastAPI, featuring a beautiful design with animations, particle effects, and persistent data storage.

## ✨ Features

### Frontend
- **Modern Design**: Elegant dark theme with gradient text and hover effects
- **Responsive Layout**: Optimized for all screen sizes
- **Particle Animation**: Interactive background with connected particles
- **Toast Notifications**: Beautiful success/error notifications
- **Smooth Animations**: CSS animations with Tailwind CSS
- **Vue.js 3**: Reactive components with Composition API

### Backend
- **FastAPI**: High-performance Python web framework
- **SQLite Database**: Persistent data storage for subscribers
- **Email Validation**: Server-side email validation
- **CORS Support**: Cross-origin resource sharing enabled
- **Admin Endpoint**: View all subscribers with timestamps

### Design Elements
- **Quantum Theme**: Three feature cards representing quantum domains
- **Gradient Effects**: Beautiful color transitions and hover states
- **Typography**: Carefully crafted text hierarchy
- **Interactive Elements**: Hover effects on cards and buttons

## Project layout

- **frontend/** – Vue.js application with modern design and animations
- **backend/** – FastAPI application with SQLite database for persistent storage

## Requirements

- Python 3.12+
- `pip` for installing dependencies

## 🚀 Quick Start

### Option 1: Docker Deployment (Recommended)
```bash
# One-command deployment
./deploy.sh

# Or step by step
docker build -t eqosystem-landing:2.0.0 .
docker run --name eqosystem-app -p 8001:8001 eqosystem-landing:2.0.0
```

### Option 2: Local Development
```bash
# Install dependencies
cd backend && pip install -r requirements.txt

# Start the server
python3 main.py
# OR
./run.sh
```

### Access the Application
- **Frontend**: http://localhost:8001
- **API Docs**: http://localhost:8001/api/docs
- **Admin Panel**: http://localhost:8001/api/subscribers

## 📡 API Endpoints

| Endpoint | Method | Description | Response |
|----------|--------|-------------|----------|
| `/api/subscribe` | POST | Subscribe email | `{message, email}` |
| `/api/subscribers` | GET | List all subscribers | `{count, subscribers[]}` |
| `/api/health` | GET | Health check | `{status, version}` |
| `/api/docs` | GET | Swagger UI | Interactive API docs |

## 💾 Data Storage & Access

### Database Location
- **Local Development**: `backend/subscribers.db`
- **Docker Container**: `/app/backend/subscribers.db`

### Accessing Data from Cursor/VS Code

#### Method 1: Data Checker Script (Recommended)
```bash
# Check local database
python3 check_data.py

# Check Docker container database
python3 check_data.py --docker

# Check via API
python3 check_data.py --api
```

#### Method 2: SQLite Extension in Cursor/VS Code
1. Install "SQLite" extension in Cursor/VS Code
2. Open `backend/subscribers.db` file
3. Right-click → "Open Database"
4. Browse tables and data visually

#### Method 3: Command Line Access
```bash
# Local development
cd backend
sqlite3 subscribers.db
.tables
SELECT * FROM subscribers;

# Docker container (copy database first)
docker cp eqosystem-app:/app/backend/subscribers.db ./docker_db.db
sqlite3 docker_db.db "SELECT * FROM subscribers;"
```

#### Method 4: Admin API
```bash
# Get all subscribers via API
curl http://localhost:8001/api/subscribers

# Pretty print JSON
curl http://localhost:8001/api/subscribers | python3 -m json.tool
```

### Database Schema
```sql
CREATE TABLE subscribers (
    id INTEGER PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    subscribed_at DATETIME NOT NULL
);
```

## 🐳 Docker Management

### Build and Deploy
```bash
# Full deployment with tests
./deploy.sh

# Build only
./deploy.sh --build-only

# Run tests
./deploy.sh --test

# Check status
./deploy.sh --status

# Clean up
./deploy.sh --clean
```

### Manual Docker Commands
```bash
# Build with custom name (no more random names!)
docker build -t eqosystem-landing:2.0.0 .

# Run with specific container name
docker run --name eqosystem-app -p 8001:8001 eqosystem-landing:2.0.0

# View logs
docker logs eqosystem-app

# Access container shell
docker exec -it eqosystem-app /bin/bash
```

## Deployment

The application is fully static on the frontend. The backend only keeps
subscribers in memory, so it is suitable for demonstrations only.

## License

See [LICENSE](LICENSE) for license information.
