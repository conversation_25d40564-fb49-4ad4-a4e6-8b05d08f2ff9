# eQosystem Landing Page v2.0

> **Beyond the Horizon** - A modern, quantum-inspired landing page built with Python and Vue.js

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/eqosystem/landing-page)
[![Python](https://img.shields.io/badge/python-3.12-blue.svg)](https://python.org)
[![Vue.js](https://img.shields.io/badge/vue.js-3.0-green.svg)](https://vuejs.org)
[![FastAPI](https://img.shields.io/badge/fastapi-0.104-teal.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://docker.com)

## 🌟 Overview

eQosystem is the emerging initiative that aims to bring minds together to drive a major scientific and technological transformation in quantum computing. This landing page showcases our three core domains:

- **🔬 Quantum Foundations** - Explore and learn the quantum world
- **💻 Quantum Software** - Develop quantum algorithms and applications  
- **🤖 Quantum Software AI** - Bridge AI and quantum computing

## ✨ Features

### 🎨 Modern Design
- **Quantum-inspired aesthetics** with gradient effects and particle animations
- **Responsive design** optimized for all devices
- **Dark theme** with elegant typography and smooth transitions
- **Interactive elements** with hover effects and micro-animations

### 🚀 Technical Excellence
- **FastAPI backend** with automatic API documentation
- **Vue.js 3 frontend** with Composition API
- **SQLite database** for persistent data storage
- **Docker containerization** for easy deployment
- **Health monitoring** and error handling

### 📧 Email Subscription
- **Real-time validation** with beautiful error states
- **Toast notifications** for user feedback
- **Duplicate prevention** and data persistence
- **Admin interface** to view subscribers

## 🚀 Quick Start

### Option 1: Docker Deployment (Recommended)

```bash
# Clone and navigate
git clone <repository-url>
cd eqosystem-landing-v2

# One-command deployment
./deploy.sh

# Access the application
open http://localhost:8001
```

### Option 2: Local Development

```bash
# Install Python dependencies
cd backend
pip install -r requirements.txt

# Start the application
python main.py

# Access at http://localhost:8001
```

## 📡 API Endpoints

| Endpoint | Method | Description | Response |
|----------|--------|-------------|----------|
| `/` | GET | Vue.js Frontend | HTML Application |
| `/api/subscribe` | POST | Subscribe email | `{message, email, id}` |
| `/api/subscribers` | GET | List subscribers | `{count, subscribers[]}` |
| `/api/health` | GET | Health check | `{status, version, ...}` |
| `/api/docs` | GET | Swagger UI | Interactive API docs |

### Example API Usage

```bash
# Subscribe to newsletter
curl -X POST http://localhost:8001/api/subscribe \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Get all subscribers
curl http://localhost:8001/api/subscribers

# Health check
curl http://localhost:8001/api/health
```

## 💾 Data Storage & Access

### Database Location
- **Local Development**: `backend/subscribers.db`
- **Docker Container**: `/app/backend/subscribers.db`

### Accessing Data from Cursor/VS Code

#### Method 1: SQLite Extension (Recommended)
1. Install "SQLite" extension in Cursor/VS Code
2. Open `backend/subscribers.db` file
3. Right-click → "Open Database"
4. Browse tables and data visually

#### Method 2: Command Line
```bash
# Local development
cd backend
sqlite3 subscribers.db "SELECT * FROM subscribers;"

# Docker container
docker exec -it eqosystem-app sqlite3 backend/subscribers.db "SELECT * FROM subscribers;"
```

#### Method 3: Python Script
```bash
# Create a simple data viewer
cd backend
python3 -c "
from database import SessionLocal, Subscriber
db = SessionLocal()
subscribers = db.query(Subscriber).all()
for s in subscribers:
    print(f'{s.id}: {s.email} ({s.subscribed_at})')
db.close()
"
```

#### Method 4: Admin API
```bash
# Pretty print JSON
curl http://localhost:8001/api/subscribers | python3 -m json.tool
```

## 🐳 Docker Management

### Build and Deploy
```bash
# Full deployment with tests
./deploy.sh

# Build only
./deploy.sh --build

# Test only
./deploy.sh --test

# Deploy only
./deploy.sh --deploy

# Clean up
./deploy.sh --clean
```

### Manual Docker Commands
```bash
# Build with specific name (no more random names!)
docker build -t eqosystem:2.0.0 .

# Run with custom container name
docker run --name eqosystem-app -p 8001:8001 eqosystem:2.0.0

# View logs
docker logs eqosystem-app

# Access container shell
docker exec -it eqosystem-app /bin/bash

# Copy database from container
docker cp eqosystem-app:/app/backend/subscribers.db ./local_copy.db
```

## 📁 Project Structure

```
eqosystem-landing-v2/
├── 🐍 backend/                 # Python FastAPI Backend
│   ├── main.py                 # Main application entry point
│   ├── database.py             # Database models and configuration
│   ├── models.py               # Pydantic request/response models
│   └── requirements.txt        # Python dependencies
├── 🎨 frontend/                # Vue.js Frontend
│   ├── index.html              # Complete SPA application
│   └── favicon.svg             # Quantum-inspired favicon
├── 🐳 Deployment
│   ├── Dockerfile              # Production container configuration
│   └── deploy.sh               # Automated deployment script
└── 📚 Documentation
    └── README.md               # This file
```

## 🎨 Design System

### Color Palette
- **Quantum Orange**: `#e76b00` (Primary brand color)
- **Quantum Purple**: `#e76ce7` (Gradient transition)
- **Quantum Blue**: `#578ef4` (Technology accent)
- **Quantum Green**: `#35f935` (Success states)

### Typography
- **Gradient Text**: eQosystem brand name with animated gradient
- **Font Weight**: Light and medium weights for elegance
- **Letter Spacing**: Wide tracking for modern feel

### Animations
- **Particle System**: Interactive background with connected dots
- **Hover Effects**: Smooth transitions on cards and buttons
- **Toast Notifications**: Slide-in animations for feedback
- **Loading States**: Elegant spinners and disabled states

## 🔧 Development

### Prerequisites
- Python 3.12+
- Docker (optional, for containerized deployment)
- Modern web browser

### Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend/requirements.txt
```

### Running Tests
```bash
# Test with deployment script
./deploy.sh --test

# Manual API testing
curl http://localhost:8001/api/health
curl -X POST http://localhost:8001/api/subscribe -H "Content-Type: application/json" -d '{"email":"<EMAIL>"}'
```

## 🚀 Production Deployment

### Docker Compose (Recommended)
```yaml
version: '3.8'
services:
  eqosystem:
    build: .
    ports:
      - "8001:8001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Environment Variables
- `PORT`: Server port (default: 8001)
- `HOST`: Server host (default: 0.0.0.0)
- `ENV`: Environment (development/production)

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:8001/api/health
```

### Metrics
- Subscriber count via API
- Application health status
- Database connectivity
- Container resource usage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🌟 About eQosystem

eQosystem is pioneering the future of quantum computing by bringing together researchers, developers, and innovators. Our mission is to make quantum technologies accessible and practical for real-world applications.

**Join us in going beyond the horizon! 🚀**

---

*Built with ❤️ by the eQosystem Team*
