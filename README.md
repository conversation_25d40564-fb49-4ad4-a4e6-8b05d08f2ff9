# 🌌 eQosystem Landing Page

> *Bringing minds together to drive quantum transformation*

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/eqosystem/landing-page)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.1-009688.svg)](https://fastapi.tiangolo.com)
[![Vue.js](https://img.shields.io/badge/Vue.js-3-4FC08D.svg)](https://vuejs.org)

A modern, quantum-themed landing page for the eQosystem initiative, featuring stunning animations and a powerful backend API.

## 🌟 Overview

**eQosystem** is an emerging initiative that aims to bring minds together to drive a major scientific and technological transformation in quantum computing. This landing page showcases our three core domains with beautiful quantum-themed animations and a fully functional newsletter subscription system.

### 🎯 Core Domains

| Domain | Description | Status |
|--------|-------------|--------|
| **🔬 Quantum Foundations** | Explore and learn the quantum world up to quantum programming | Coming Soon |
| **💻 Quantum Software** | Develop the new generation of software based on quantum algorithms | Coming Soon |
| **🤖 Quantum Software AI** | Create synergies between AI, quantum algorithms, and quantum computing power | Coming Soon |

## ✨ Features

### 🎨 Frontend Excellence
- **🌌 Quantum Animations**: Mesmerizing particle systems, energy waves, and quantum grids
- **📱 Responsive Design**: Perfect on all devices from mobile to desktop
- **⚡ Vue.js 3**: Modern reactive framework with component architecture
- **🎭 Dark Theme**: Elegant quantum-inspired design with gradient effects
- **📧 Smart Forms**: Real-time email validation with toast notifications
- **🚀 Performance**: Optimized animations and efficient rendering

### 🔧 Backend Power
- **⚡ FastAPI**: High-performance async Python web framework
- **🗄️ SQLite Database**: Lightweight persistence with SQLAlchemy ORM
- **📊 API Documentation**: Auto-generated Swagger/OpenAPI docs
- **🔒 Security**: Input validation, CORS, and secure error handling
- **📈 Monitoring**: Health checks and comprehensive logging
- **🔄 Real-time**: Instant feedback and data synchronization

## 🚀 Quick Start

### ⚡ One-Command Setup

```bash
# Clone and start the application
git clone https://github.com/beyondhorizon-mpg/eqosystem-landing-page-v1.0.git
cd eqosystem-landing-page-v1.0
./deploy.sh
```

🎉 **That's it!** Open http://localhost:8002 in your browser.

> **🔒 Security Note**: The admin endpoint `/api/subscribers` is protected by HTTP Basic Authentication. Default credentials are `admin:eQosystem2024!` - **change these in production!**

### 🐳 Docker Quick Start

```bash
# Using Docker (recommended for production)
docker build -t eqosystem-landing .
docker run -p 8002:8002 eqosystem-landing

# Using Docker Compose
docker-compose up --build
```

### 🛠️ Local Development

```bash
# Setup development environment
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# Start development server with auto-reload
python main.py

# Access at http://localhost:8002
```

## 📊 API Reference

### 🔗 Endpoints

| Method | Endpoint | Description | Authentication | Response |
|--------|----------|-------------|----------------|----------|
| `GET` | `/` | Vue.js Frontend | None | HTML Application |
| `POST` | `/api/subscribe` | Subscribe to newsletter | None | `{message, email, id}` |
| `GET` | `/api/subscribers` | List all subscribers | **🔒 Admin Only** | `{count, subscribers[]}` |
| `GET` | `/api/health` | Health check | None | `{status, version, ...}` |
| `GET` | `/api/docs` | Swagger UI | None | Interactive API docs |

### 📖 Interactive Documentation

- **Swagger UI**: http://localhost:8002/api/docs
- **ReDoc**: http://localhost:8002/api/redoc

### 🧪 API Testing

```bash
# Subscribe to newsletter
curl -X POST "http://localhost:8002/api/subscribe" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'

# Health check
curl http://localhost:8002/api/health

# List subscribers (admin only - requires authentication)
curl -u admin:eQosystem2024! http://localhost:8002/api/subscribers
```

## 🔒 Security

### 🛡️ Security Features

- ✅ **Input Validation**: Pydantic models with email validation
- ✅ **SQL Injection Prevention**: SQLAlchemy ORM with parameterized queries
- ✅ **XSS Protection**: Input sanitization and output encoding
- ✅ **CORS Configuration**: Configurable cross-origin policies
- ✅ **Error Handling**: Secure error messages without information leakage
- ✅ **Rate Limiting**: Built-in request throttling
- ✅ **HTTPS Ready**: TLS/SSL support for production
- ✅ **Admin Authentication**: HTTP Basic Auth for sensitive endpoints

### 🔑 Admin Access

The `/api/subscribers` endpoint is protected by HTTP Basic Authentication:

```bash
# Default credentials (CHANGE IN PRODUCTION!)
Username: admin
Password: eQosystem2024!

# Environment variables for production
export ADMIN_USERNAME="your-secure-username"
export ADMIN_PASSWORD="your-secure-password"
```

### 🔍 Security Best Practices

```python
# ✅ Secure database queries
subscriber = db.query(Subscriber).filter(Subscriber.email == email).first()

# ✅ Environment-based configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./subscribers.db")

# ✅ Input validation
class SubscribeRequest(BaseModel):
    email: EmailStr = Field(..., description="Valid email address")
```

## 💾 Data Storage & Access

### Database Location
- **Local Development**: `backend/subscribers.db`
- **Docker Container**: `/app/backend/subscribers.db`

### Accessing Data from Cursor/VS Code

#### Method 1: SQLite Extension (Recommended)
1. Install "SQLite" extension in Cursor/VS Code
2. Open `backend/subscribers.db` file
3. Right-click → "Open Database"
4. Browse tables and data visually

#### Method 2: Command Line
```bash
# Local development
cd backend
sqlite3 subscribers.db "SELECT * FROM subscribers;"

# Docker container
docker exec -it eqosystem-app sqlite3 backend/subscribers.db "SELECT * FROM subscribers;"
```

#### Method 3: Python Script
```bash
# Create a simple data viewer
cd backend
python3 -c "
from database import SessionLocal, Subscriber
db = SessionLocal()
subscribers = db.query(Subscriber).all()
for s in subscribers:
    print(f'{s.id}: {s.email} ({s.subscribed_at})')
db.close()
"
```

#### Method 4: Admin API (Secure)
```bash
# Pretty print JSON with authentication
curl -u admin:eQosystem2024! http://localhost:8002/api/subscribers | python3 -m json.tool
```

### 📊 Database Management

```bash
# View all subscribers
python manage_data.py --show

# View subscribers via API (with authentication)
python manage_data.py --show --api

# View Docker container data
python manage_data.py --show --docker

# Export subscriber data
python manage_data.py --export subscribers.json

# Database statistics
python manage_data.py --count

# Test API endpoints
python manage_data.py --test-api
```

## 🐳 Docker Management

### Build and Deploy
```bash
# Full deployment with tests
./deploy.sh

# Build only
./deploy.sh --build

# Test only
./deploy.sh --test

# Deploy only
./deploy.sh --deploy

# Clean up
./deploy.sh --clean
```

### Manual Docker Commands
```bash
# Build with specific name
docker build -t eqosystem:1.0.0 .

# Run with custom container name
docker run --name eqosystem-app -p 8002:8002 eqosystem:1.0.0

# View logs
docker logs eqosystem-app

# Access container shell
docker exec -it eqosystem-app /bin/bash

# Copy database from container
docker cp eqosystem-app:/app/backend/subscribers.db ./local_copy.db
```

## 📁 Project Structure

```
eqosystem-landing-page-v1.0/
├── 🎨 frontend/                 # Vue.js quantum-themed UI
│   ├── index.html              # Single-page application
│   ├── favicon.svg             # eQosystem branding
│   └── quantum-bg.svg          # Quantum background assets
├── ⚙️ backend/                  # FastAPI quantum API
│   ├── main.py                 # Application entry point
│   ├── database.py             # SQLAlchemy models & config
│   ├── models.py               # Pydantic request/response models
│   └── requirements.txt        # Python dependencies
├── 🐳 Infrastructure
│   ├── Dockerfile              # Multi-stage container build
│   ├── docker-compose.yml      # Development orchestration
│   └── deploy.sh               # Automated deployment script
├── 📚 Documentation
│   ├── README.md               # You are here!
│   ├── CHANGELOG.md            # Version history
│   ├── CONTRIBUTING.md         # Contribution guidelines
│   ├── SECURITY.md             # Security policy
│   └── LICENSE                 # MIT license
├── 🔧 Configuration
│   ├── requirements.txt        # Main project dependencies
│   ├── .env.example            # Environment configuration template
│   ├── .gitignore             # Git ignore rules
│   ├── .dockerignore          # Docker ignore rules
│   └── manage_data.py          # Database management utilities
```

## 🎨 Design System

### Color Palette
- **Quantum Orange**: `#e76b00` (Primary brand color)
- **Quantum Purple**: `#e76ce7` (Gradient transition)
- **Quantum Blue**: `#578ef4` (Technology accent)
- **Quantum Green**: `#35f935` (Success states)

### Typography
- **Gradient Text**: eQosystem brand name with animated gradient
- **Font Weight**: Light and medium weights for elegance
- **Letter Spacing**: Wide tracking for modern feel

### Animations
- **Particle System**: Interactive background with connected dots
- **Hover Effects**: Smooth transitions on cards and buttons
- **Toast Notifications**: Slide-in animations for feedback
- **Loading States**: Elegant spinners and disabled states

## 🔧 Development

### Prerequisites
- Python 3.12+
- Docker (optional, for containerized deployment)
- Modern web browser

### Environment Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r backend/requirements.txt
```

### Running Tests
```bash
# Test with deployment script
./deploy.sh --test

# Manual API testing
curl http://localhost:8002/api/health
curl -X POST http://localhost:8002/api/subscribe -H "Content-Type: application/json" -d '{"email":"<EMAIL>"}'
```

## 🚀 Production Deployment

### 🌐 Production Deployment

```bash
# 1. Set production environment
export DATABASE_URL=sqlite:///./data/subscribers.db
export CORS_ORIGINS=https://yourdomain.com
export DEBUG=false
export ADMIN_USERNAME="your-secure-admin"
export ADMIN_PASSWORD="your-secure-password"

# 2. Deploy with Docker (recommended)
docker build -t eqosystem-landing .
docker run -d -p 80:8002 \
  -e DATABASE_URL=sqlite:///./data/subscribers.db \
  -e CORS_ORIGINS=https://yourdomain.com \
  -e ADMIN_USERNAME="your-secure-admin" \
  -e ADMIN_PASSWORD="your-secure-password" \
  -v $(pwd)/data:/app/data \
  eqosystem-landing

# 3. Verify deployment
curl https://yourdomain.com/api/health
```

### ☁️ Cloud Platforms

| Platform | Deployment Method | Documentation |
|----------|------------------|---------------|
| **Heroku** | `git push heroku main` | [Heroku Guide](https://devcenter.heroku.com/articles/container-registry-and-runtime) |
| **AWS** | ECS/Lambda | [AWS Guide](https://aws.amazon.com/getting-started/hands-on/deploy-docker-containers/) |
| **Google Cloud** | Cloud Run | [GCP Guide](https://cloud.google.com/run/docs/quickstarts/build-and-deploy/deploy-python-service) |
| **Azure** | Container Instances | [Azure Guide](https://docs.microsoft.com/en-us/azure/container-instances/) |
| **DigitalOcean** | App Platform | [DO Guide](https://docs.digitalocean.com/products/app-platform/) |

### 🔧 Configuration

#### Environment Variables
```bash
# Server Configuration
HOST=0.0.0.0                    # Server host
PORT=8002                       # Server port

# Database Configuration
DATABASE_URL=sqlite:///./subscribers.db  # Database connection

# Security Configuration
ADMIN_USERNAME=admin            # Admin username (change in production!)
ADMIN_PASSWORD=eQosystem2024!   # Admin password (change in production!)
CORS_ORIGINS=*                  # CORS origins (use specific domains in production)
DEBUG=false                     # Debug mode (never true in production)
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:8002/api/health
```

### Metrics
- Subscriber count via API
- Application health status
- Database connectivity
- Container resource usage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🌟 About eQosystem

eQosystem is pioneering the future of quantum computing by bringing together researchers, developers, and innovators. Our mission is to make quantum technologies accessible and practical for real-world applications.

**Join us in going beyond the horizon! 🚀**

## 🌐 Application URLs

### 📞 Contact Information

- **🌐 Website**: [eqosystem.com](https://eqosystem.com)
- **📧 Email**: <EMAIL>
- **🐛 Issues**: [GitHub Issues](https://github.com/beyondhorizon-mpg/eqosystem-landing-page-v1.0/issues)
- **💬 Discussions**: [GitHub Discussions](https://github.com/beyondhorizon-mpg/eqosystem-landing-page-v1.0/discussions)

### 🔗 Useful Links

- **📚 Documentation**: [docs.eqosystem.com](https://docs.eqosystem.com)
- **🎓 Quantum Learning**: [learn.eqosystem.com](https://learn.eqosystem.com)
- **💻 Developer Portal**: [dev.eqosystem.com](https://dev.eqosystem.com)
- **📰 Blog**: [blog.eqosystem.com](https://blog.eqosystem.com)

### 🙏 Acknowledgments

#### 🏆 Technology Stack

- **[Vue.js](https://vuejs.org)** - Progressive JavaScript framework
- **[FastAPI](https://fastapi.tiangolo.com)** - Modern Python web framework
- **[Tailwind CSS](https://tailwindcss.com)** - Utility-first CSS framework
- **[SQLAlchemy](https://sqlalchemy.org)** - Python SQL toolkit and ORM
- **[Pydantic](https://pydantic-docs.helpmanual.io)** - Data validation library
- **[Uvicorn](https://uvicorn.org)** - Lightning-fast ASGI server

#### 🌟 Special Thanks

- **Quantum Computing Community** - For inspiration and continuous innovation
- **Open Source Contributors** - For making this project possible
- **Early Adopters** - For testing and providing valuable feedback

---

<div align="center">

**🚀 Built with ❤️ for the quantum computing community 🚀**

*"The future is quantum, and it starts here."*

[![Star this repo](https://img.shields.io/github/stars/beyondhorizon-mpg/eqosystem-landing-page-v1.0?style=social)](https://github.com/beyondhorizon-mpg/eqosystem-landing-page-v1.0)
[![Follow @eQosystem](https://img.shields.io/twitter/follow/eQosystem?style=social)](https://twitter.com/eQosystem)

</div>
