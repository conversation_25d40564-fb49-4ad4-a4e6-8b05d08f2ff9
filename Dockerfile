# =============================================================================
# eQosystem Landing Page - Docker Configuration
# =============================================================================
#
# Multi-stage Docker build for optimized production deployment
#
# Build: docker build -t eqosystem-landing:2.0.0 .
# Run:   docker run --name eqosystem-app -p 8001:8001 eqosystem-landing:2.0.0
#
# =============================================================================

# Use official Python slim image for smaller size
FROM python:3.12-slim as base

# Set metadata labels
LABEL maintainer="eQosystem Team"
LABEL version="2.0.0"
LABEL description="eQosystem Landing Page with Vue.js and FastAPI"

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PORT=8001
ENV HOST=0.0.0.0

# Create non-root user for security
RUN groupadd -r eqosystem && useradd -r -g eqosystem eqosystem

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY backend/requirements.txt ./requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend ./backend
COPY frontend ./frontend

# Create directory for database and set permissions
RUN mkdir -p /app/data && \
    chown -R eqosystem:eqosystem /app

# Switch to non-root user
USER eqosystem

# Expose port
EXPOSE $PORT

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/api/health || exit 1

# Set the startup command
CMD ["sh", "backend/run.sh"]
