# =============================================================================
# eQosystem Landing Page - Production Docker Image
# =============================================================================
#
# Multi-stage build for optimized production deployment
# 
# Build: docker build -t eqosystem:1.0.0 .
# Run:   docker run --name eqosystem-app -p 8002:8002 eqosystem:1.0.0
#
# =============================================================================

FROM python:3.9-slim

# =============================================================================
# METADATA AND LABELS
# =============================================================================

LABEL maintainer="eQosystem Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="eQosystem Landing Page - Quantum Computing Initiative"
LABEL org.opencontainers.image.title="eQosystem Landing Page"
LABEL org.opencontainers.image.description="Modern landing page for quantum computing initiative"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="eQosystem"

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Python environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Application configuration
ENV PORT=8002
ENV HOST=0.0.0.0
ENV ENV=production

# =============================================================================
# SYSTEM SETUP
# =============================================================================

# Create application user for security
RUN groupadd -r eqosystem && \
    useradd -r -g eqosystem -d /app -s /bin/bash eqosystem

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# =============================================================================
# APPLICATION SETUP
# =============================================================================

# Set working directory
WORKDIR /app

# Copy and install Python dependencies
COPY requirements.txt ./requirements.txt
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ ./backend/
COPY frontend/ ./frontend/

# Create data directory for SQLite database
RUN mkdir -p /app/data

# Set proper ownership
RUN chown -R eqosystem:eqosystem /app

# Switch to non-root user
USER eqosystem

# =============================================================================
# RUNTIME CONFIGURATION
# =============================================================================

# Expose application port
EXPOSE $PORT

# Health check for container orchestration
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/api/health || exit 1

# Working directory for runtime
WORKDIR /app/backend

# Default command
CMD ["python", "main.py"]
