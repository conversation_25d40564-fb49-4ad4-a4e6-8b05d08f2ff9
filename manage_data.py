#!/usr/bin/env python3
"""
eQosystem Data Management Utility

This script provides comprehensive data management capabilities for the
eQosystem landing page, including database inspection, backup, and administration.

Usage:
    python3 manage_data.py --help
    python3 manage_data.py --show                    # Show all subscribers
    python3 manage_data.py --count                   # Show subscriber count
    python3 manage_data.py --export subscribers.json # Export to JSON
    python3 manage_data.py --docker                  # Manage Docker data
    python3 manage_data.py --api                     # Test API endpoints

Author: eQosystem Team
Version: 1.0.0
"""

import argparse
import json
import sqlite3
import sys
import os
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# Colors for beautiful output
class Colors:
    BLUE = '\033[0;34m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    RED = '\033[0;31m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def log_info(message: str) -> None:
    """Print info message with blue color."""
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.NC}")

def log_success(message: str) -> None:
    """Print success message with green color."""
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def log_warning(message: str) -> None:
    """Print warning message with yellow color."""
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.NC}")

def log_error(message: str) -> None:
    """Print error message with red color."""
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def log_header(message: str) -> None:
    """Print header message with purple color."""
    print(f"{Colors.PURPLE}🚀 {message}{Colors.NC}")

class DatabaseManager:
    """Manages local SQLite database operations."""
    
    def __init__(self, db_path: str = "backend/subscribers.db"):
        self.db_path = Path(db_path)
        
    def exists(self) -> bool:
        """Check if database file exists."""
        return self.db_path.exists()
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        if not self.exists():
            raise FileNotFoundError(f"Database not found at {self.db_path}")
        return sqlite3.connect(self.db_path)
    
    def get_subscribers(self) -> List[Dict[str, Any]]:
        """Get all subscribers from database."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, email, subscribed_at 
                FROM subscribers 
                ORDER BY subscribed_at DESC
            """)
            
            return [
                {
                    "id": row[0],
                    "email": row[1],
                    "subscribed_at": row[2]
                }
                for row in cursor.fetchall()
            ]
    
    def get_count(self) -> int:
        """Get total subscriber count."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM subscribers")
            return cursor.fetchone()[0]
    
    def export_to_json(self, output_file: str) -> None:
        """Export subscribers to JSON file."""
        subscribers = self.get_subscribers()
        
        export_data = {
            "export_date": datetime.now().isoformat(),
            "total_subscribers": len(subscribers),
            "subscribers": subscribers
        }
        
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        log_success(f"Exported {len(subscribers)} subscribers to {output_file}")

class DockerManager:
    """Manages Docker container database operations."""
    
    def __init__(self, container_name: str = "eqosystem-app"):
        self.container_name = container_name
    
    def is_running(self) -> bool:
        """Check if Docker container is running."""
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={self.container_name}", "--format", "{{.Names}}"],
                capture_output=True, text=True, check=True
            )
            return self.container_name in result.stdout
        except subprocess.CalledProcessError:
            return False
    
    def copy_database(self, local_path: str = "/tmp/docker_subscribers.db") -> str:
        """Copy database from Docker container to local filesystem."""
        if not self.is_running():
            raise RuntimeError(f"Container {self.container_name} is not running")
        
        subprocess.run([
            "docker", "cp", 
            f"{self.container_name}:/app/backend/subscribers.db", 
            local_path
        ], check=True)
        
        return local_path
    
    def get_subscribers(self) -> List[Dict[str, Any]]:
        """Get subscribers from Docker container database."""
        temp_db = self.copy_database()
        try:
            db_manager = DatabaseManager(temp_db)
            return db_manager.get_subscribers()
        finally:
            if os.path.exists(temp_db):
                os.remove(temp_db)

class APIManager:
    """Manages API endpoint testing and data retrieval."""
    
    def __init__(self, base_url: str = "http://localhost:8002",
                 admin_username: str = "admin", admin_password: str = "eQosystem2024!"):
        self.base_url = base_url
        self.admin_username = admin_username
        self.admin_password = admin_password
    
    def test_health(self) -> Dict[str, Any]:
        """Test health endpoint."""
        import urllib.request
        import urllib.error
        
        try:
            with urllib.request.urlopen(f"{self.base_url}/api/health") as response:
                return json.loads(response.read().decode())
        except urllib.error.URLError as e:
            raise ConnectionError(f"Cannot connect to API: {e}")
    
    def get_subscribers(self) -> Dict[str, Any]:
        """Get subscribers via API with admin authentication."""
        import urllib.request
        import base64

        # Create HTTP Basic Auth header
        credentials = f"{self.admin_username}:{self.admin_password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()

        req = urllib.request.Request(f"{self.base_url}/api/subscribers")
        req.add_header("Authorization", f"Basic {encoded_credentials}")

        try:
            with urllib.request.urlopen(req) as response:
                return json.loads(response.read().decode())
        except urllib.error.HTTPError as e:
            if e.code == 401:
                raise ConnectionError("Authentication failed. Check admin credentials.")
            raise ConnectionError(f"API request failed: {e}")
    
    def test_subscription(self, email: str = "<EMAIL>") -> Dict[str, Any]:
        """Test subscription endpoint."""
        import urllib.request
        
        data = json.dumps({"email": email}).encode('utf-8')
        req = urllib.request.Request(
            f"{self.base_url}/api/subscribe",
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        try:
            with urllib.request.urlopen(req) as response:
                return json.loads(response.read().decode())
        except urllib.error.HTTPError as e:
            if e.code == 400:
                return {"error": "Email already subscribed"}
            raise

def show_subscribers(source: str = "local", admin_user: str = "admin", admin_pass: str = "eQosystem2024!") -> None:
    """Display all subscribers in a formatted table."""
    log_header("eQosystem Subscribers")
    
    try:
        if source == "local":
            db_manager = DatabaseManager()
            if not db_manager.exists():
                log_error("Local database not found. Run the application first or use --docker flag.")
                return
            subscribers = db_manager.get_subscribers()
            
        elif source == "docker":
            docker_manager = DockerManager()
            if not docker_manager.is_running():
                log_error("Docker container 'eqosystem-app' is not running")
                return
            subscribers = docker_manager.get_subscribers()
            
        elif source == "api":
            api_manager = APIManager(admin_username=admin_user, admin_password=admin_pass)
            data = api_manager.get_subscribers()
            subscribers = data["subscribers"]
        
        if not subscribers:
            log_warning("No subscribers found")
            return
        
        # Display table
        print(f"\n📊 Total subscribers: {len(subscribers)}")
        print("=" * 80)
        print(f"{'ID':<5} {'Email':<35} {'Subscribed At':<25}")
        print("-" * 80)
        
        for sub in subscribers:
            print(f"{sub['id']:<5} {sub['email']:<35} {sub['subscribed_at']:<25}")
        
        print("=" * 80)
        log_success(f"Displayed {len(subscribers)} subscribers from {source}")
        
    except Exception as e:
        log_error(f"Failed to retrieve subscribers: {e}")

def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(
        description="eQosystem Data Management Utility",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 manage_data.py --show                    # Show local database
  python3 manage_data.py --show --docker           # Show Docker database
  python3 manage_data.py --show --api              # Show via API
  python3 manage_data.py --count                   # Show count only
  python3 manage_data.py --export backup.json     # Export to JSON
  python3 manage_data.py --test-api                # Test all API endpoints
        """
    )
    
    parser.add_argument("--show", action="store_true", help="Show all subscribers")
    parser.add_argument("--count", action="store_true", help="Show subscriber count only")
    parser.add_argument("--export", metavar="FILE", help="Export subscribers to JSON file")
    parser.add_argument("--docker", action="store_true", help="Use Docker container database")
    parser.add_argument("--api", action="store_true", help="Use API endpoints")
    parser.add_argument("--test-api", action="store_true", help="Test API endpoints")
    parser.add_argument("--admin-user", default="admin", help="Admin username for API access")
    parser.add_argument("--admin-pass", default="eQosystem2024!", help="Admin password for API access")
    
    args = parser.parse_args()
    
    # Determine data source
    if args.docker:
        source = "docker"
    elif args.api:
        source = "api"
    else:
        source = "local"
    
    try:
        if args.show:
            show_subscribers(source, args.admin_user, args.admin_pass)
            
        elif args.count:
            if source == "local":
                db_manager = DatabaseManager()
                count = db_manager.get_count()
            elif source == "docker":
                docker_manager = DockerManager()
                subscribers = docker_manager.get_subscribers()
                count = len(subscribers)
            elif source == "api":
                api_manager = APIManager(admin_username=args.admin_user, admin_password=args.admin_pass)
                data = api_manager.get_subscribers()
                count = data["count"]
            
            log_success(f"Total subscribers ({source}): {count}")
            
        elif args.export:
            if source != "local":
                log_error("Export is only supported for local database")
                sys.exit(1)
            
            db_manager = DatabaseManager()
            db_manager.export_to_json(args.export)
            
        elif args.test_api:
            log_header("Testing API Endpoints")
            api_manager = APIManager(admin_username=args.admin_user, admin_password=args.admin_pass)
            
            # Test health
            try:
                health = api_manager.test_health()
                log_success(f"Health check: {health['status']} (v{health['version']})")
            except Exception as e:
                log_error(f"Health check failed: {e}")
            
            # Test subscribers endpoint
            try:
                data = api_manager.get_subscribers()
                log_success(f"Subscribers endpoint: {data['count']} subscribers")
            except Exception as e:
                log_error(f"Subscribers endpoint failed: {e}")
            
            # Test subscription (might fail if email exists)
            try:
                result = api_manager.test_subscription()
                if "error" in result:
                    log_warning(f"Subscription test: {result['error']}")
                else:
                    log_success(f"Subscription test: {result['message']}")
            except Exception as e:
                log_error(f"Subscription test failed: {e}")
        
        else:
            # Default: show help
            parser.print_help()
    
    except KeyboardInterrupt:
        log_warning("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        log_error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
