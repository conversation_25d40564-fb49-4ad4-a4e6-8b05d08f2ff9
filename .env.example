# =============================================================================
# eQosystem Landing Page - Environment Configuration
# =============================================================================
#
# This file contains example environment variables for the eQosystem landing page.
# Copy this file to .env and modify the values as needed for your deployment.
#
# Security Note: Never commit .env files with real credentials to version control!
#
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Server configuration
HOST=0.0.0.0
PORT=8002
ENV=production

# CORS configuration (comma-separated list of allowed origins)
# For production, specify exact domains instead of "*"
CORS_ORIGINS=*

# =============================================================================
# ADMIN AUTHENTICATION
# =============================================================================

# Admin credentials for accessing protected endpoints
# IMPORTANT: Change these default values in production!
ADMIN_USERNAME=admin
ADMIN_PASSWORD=eQosystem2024!

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQLite database path (relative to backend directory)
DATABASE_URL=sqlite:///./subscribers.db

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable/disable debug mode (never enable in production)
DEBUG=false

# Session secret key (generate a random string for production)
SECRET_KEY=your-secret-key-here

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker build arguments
BUILD_DATE=
VERSION=1.0.0
VCS_REF=

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
#
# For production deployment:
#
# 1. Change ADMIN_USERNAME and ADMIN_PASSWORD to secure values
# 2. Set CORS_ORIGINS to your specific domain(s)
# 3. Ensure DEBUG=false
# 4. Generate a strong SECRET_KEY
# 5. Consider using environment-specific .env files:
#    - .env.development
#    - .env.staging  
#    - .env.production
#
# 6. Never commit .env files to version control
# 7. Use secrets management in cloud deployments
#
# =============================================================================
