#!/bin/bash
# =============================================================================
# eQosystem Landing Page - Deployment Script
# =============================================================================
#
# This script handles the complete deployment process for the eQosystem
# landing page, including building, testing, and running the application.
#
# Usage:
#   ./deploy.sh                 # Full deployment
#   ./deploy.sh --build-only    # Build Docker image only
#   ./deploy.sh --test          # Run tests only
#   ./deploy.sh --clean         # Clean up containers and images
#
# =============================================================================

set -e  # Exit on any error

# Configuration
APP_NAME="eqosystem-landing"
APP_VERSION="2.0.0"
CONTAINER_NAME="eqosystem-app"
IMAGE_NAME="${APP_NAME}:${APP_VERSION}"
PORT=8001

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Clean up existing containers and images
cleanup() {
    log_info "Cleaning up existing containers and images..."
    
    # Stop and remove container if it exists
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Stopping and removing existing container: ${CONTAINER_NAME}"
        docker stop "${CONTAINER_NAME}" 2>/dev/null || true
        docker rm "${CONTAINER_NAME}" 2>/dev/null || true
    fi
    
    # Remove image if it exists
    if docker images --format 'table {{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"; then
        log_info "Removing existing image: ${IMAGE_NAME}"
        docker rmi "${IMAGE_NAME}" 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image: ${IMAGE_NAME}"
    
    docker build \
        --tag "${IMAGE_NAME}" \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${APP_VERSION}" \
        .
    
    log_success "Docker image built successfully"
}

# Run tests
run_tests() {
    log_info "Running application tests..."
    
    # Start container for testing
    docker run --rm --name "${CONTAINER_NAME}-test" \
        -p "${PORT}:${PORT}" \
        -d "${IMAGE_NAME}"
    
    # Wait for application to start
    sleep 5
    
    # Test health endpoint
    if curl -f "http://localhost:${PORT}/api/health" > /dev/null 2>&1; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        docker stop "${CONTAINER_NAME}-test" 2>/dev/null || true
        exit 1
    fi
    
    # Test subscription endpoint
    if curl -X POST "http://localhost:${PORT}/api/subscribe" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>"}' > /dev/null 2>&1; then
        log_success "Subscription endpoint test passed"
    else
        log_error "Subscription endpoint test failed"
        docker stop "${CONTAINER_NAME}-test" 2>/dev/null || true
        exit 1
    fi
    
    # Stop test container
    docker stop "${CONTAINER_NAME}-test" 2>/dev/null || true
    
    log_success "All tests passed"
}

# Deploy application
deploy() {
    log_info "Deploying application..."
    
    # Run the container with proper name
    docker run -d \
        --name "${CONTAINER_NAME}" \
        -p "${PORT}:${PORT}" \
        --restart unless-stopped \
        "${IMAGE_NAME}"
    
    log_success "Application deployed successfully"
    log_info "Application is running at: http://localhost:${PORT}"
    log_info "API documentation: http://localhost:${PORT}/api/docs"
    log_info "Admin endpoint: http://localhost:${PORT}/api/subscribers"
}

# Show application status
show_status() {
    log_info "Application Status:"
    echo ""
    
    if docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep -q "${CONTAINER_NAME}"; then
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "${CONTAINER_NAME}"
        echo ""
        log_success "Application is running"
        
        # Show database info
        log_info "Database location: Inside container at /app/backend/subscribers.db"
        log_info "To access database: docker exec -it ${CONTAINER_NAME} sqlite3 backend/subscribers.db"
    else
        log_warning "Application is not running"
    fi
}

# Main deployment function
main() {
    echo "🚀 eQosystem Landing Page Deployment"
    echo "====================================="
    echo ""
    
    check_docker
    
    case "${1:-}" in
        --build-only)
            cleanup
            build_image
            ;;
        --test)
            run_tests
            ;;
        --clean)
            cleanup
            ;;
        --status)
            show_status
            ;;
        *)
            cleanup
            build_image
            run_tests
            deploy
            show_status
            ;;
    esac
}

# Run main function with all arguments
main "$@"
