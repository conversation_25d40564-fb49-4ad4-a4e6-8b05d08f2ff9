#!/bin/bash
# =============================================================================
# eQosystem Landing Page - Deployment Script v2.0
# =============================================================================
#
# Complete deployment automation for the eQosystem landing page.
# Handles building, testing, and running with proper container naming.
#
# Usage:
#   ./deploy.sh                 # Full deployment
#   ./deploy.sh --build         # Build only
#   ./deploy.sh --test          # Test only
#   ./deploy.sh --run           # Run only
#   ./deploy.sh --clean         # Clean up
#   ./deploy.sh --status        # Show status
#
# =============================================================================

set -e  # Exit on any error

# =============================================================================
# CONFIGURATION
# =============================================================================

APP_NAME="eqosystem"
APP_VERSION="2.0.0"
CONTAINER_NAME="eqosystem-app"
IMAGE_NAME="${APP_NAME}:${APP_VERSION}"
PORT=8001
HEALTH_ENDPOINT="http://localhost:${PORT}/api/health"

# Colors for beautiful output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Check if Docker is available and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    log_success "Docker is available and running"
}

# Clean up existing containers and images
cleanup() {
    log_header "Cleaning up existing resources..."
    
    # Stop and remove container if it exists
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Stopping and removing container: ${CONTAINER_NAME}"
        docker stop "${CONTAINER_NAME}" 2>/dev/null || true
        docker rm "${CONTAINER_NAME}" 2>/dev/null || true
        log_success "Container removed"
    fi
    
    # Remove image if it exists
    if docker images --format 'table {{.Repository}}:{{.Tag}}' | grep -q "^${IMAGE_NAME}$"; then
        log_info "Removing image: ${IMAGE_NAME}"
        docker rmi "${IMAGE_NAME}" 2>/dev/null || true
        log_success "Image removed"
    fi
    
    # Clean up dangling images
    docker image prune -f > /dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Build Docker image
build_image() {
    log_header "Building Docker image: ${IMAGE_NAME}"
    
    # Build with build args and labels
    docker build \
        --tag "${IMAGE_NAME}" \
        --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --label "build.version=${APP_VERSION}" \
        --label "build.commit=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
        . || {
        log_error "Docker build failed"
        exit 1
    }
    
    log_success "Docker image built successfully"
    
    # Show image info
    docker images "${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# Test the application
test_application() {
    log_header "Testing application..."
    
    # Start test container
    log_info "Starting test container..."
    docker run --rm --name "${CONTAINER_NAME}-test" \
        -p "${PORT}:${PORT}" \
        -d "${IMAGE_NAME}" || {
        log_error "Failed to start test container"
        exit 1
    }
    
    # Wait for application to start
    log_info "Waiting for application to start..."
    sleep 8
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if curl -f -s "${HEALTH_ENDPOINT}" > /dev/null; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        docker stop "${CONTAINER_NAME}-test" 2>/dev/null || true
        exit 1
    fi
    
    # Test subscription endpoint
    log_info "Testing subscription endpoint..."
    if curl -X POST -s -f "${HEALTH_ENDPOINT%/health}/subscribe" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>"}' > /dev/null; then
        log_success "Subscription endpoint test passed"
    else
        log_warning "Subscription endpoint test failed (might be expected if email exists)"
    fi
    
    # Stop test container
    docker stop "${CONTAINER_NAME}-test" 2>/dev/null || true
    
    log_success "Application tests completed"
}

# Deploy the application
deploy_application() {
    log_header "Deploying application..."
    
    # Run the container with proper configuration
    docker run -d \
        --name "${CONTAINER_NAME}" \
        -p "${PORT}:${PORT}" \
        --restart unless-stopped \
        --label "app=eqosystem" \
        --label "version=${APP_VERSION}" \
        "${IMAGE_NAME}" || {
        log_error "Failed to start application container"
        exit 1
    }
    
    # Wait for application to be ready
    log_info "Waiting for application to be ready..."
    sleep 5
    
    # Verify deployment
    if curl -f -s "${HEALTH_ENDPOINT}" > /dev/null; then
        log_success "Application deployed successfully"
    else
        log_error "Application deployment verification failed"
        docker logs "${CONTAINER_NAME}"
        exit 1
    fi
}

# Show application status
show_status() {
    log_header "Application Status"
    echo ""
    
    # Check if container is running
    if docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep -q "${CONTAINER_NAME}"; then
        echo "📊 Container Status:"
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep "${CONTAINER_NAME}"
        echo ""
        
        # Test health endpoint
        if curl -f -s "${HEALTH_ENDPOINT}" > /dev/null; then
            echo "🟢 Application: Healthy"
            echo "🌐 Frontend: http://localhost:${PORT}"
            echo "📚 API Docs: http://localhost:${PORT}/api/docs"
            echo "👥 Subscribers: http://localhost:${PORT}/api/subscribers"
            echo ""
            
            # Show health details
            echo "📋 Health Details:"
            curl -s "${HEALTH_ENDPOINT}" | python3 -m json.tool 2>/dev/null || echo "Unable to parse health response"
        else
            echo "🔴 Application: Unhealthy"
        fi
    else
        echo "🔴 Container: Not running"
        echo ""
        echo "Start with: ./deploy.sh --run"
    fi
    
    echo ""
    echo "💾 Database: SQLite in container at /app/backend/subscribers.db"
    echo "🔍 View data: docker exec -it ${CONTAINER_NAME} python3 -c \"from database import *; db=SessionLocal(); print([s.email for s in db.query(Subscriber).all()])\""
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    echo ""
    log_header "eQosystem Landing Page Deployment v${APP_VERSION}"
    echo "=================================================="
    echo ""
    
    check_docker
    
    case "${1:-}" in
        --build)
            cleanup
            build_image
            ;;
        --test)
            test_application
            ;;
        --run)
            deploy_application
            show_status
            ;;
        --clean)
            cleanup
            ;;
        --status)
            show_status
            ;;
        *)
            # Full deployment
            cleanup
            build_image
            test_application
            deploy_application
            show_status
            ;;
    esac
    
    echo ""
    log_success "Deployment script completed!"
}

# Execute main function with all arguments
main "$@"
