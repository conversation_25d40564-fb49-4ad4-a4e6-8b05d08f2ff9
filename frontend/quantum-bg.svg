<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Quantum particle glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Quantum wave pattern -->
    <filter id="wave-glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Gradient definitions for quantum effects -->
    <radialGradient id="quantumGlow1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3"/>
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0"/>
    </radialGradient>
    
    <radialGradient id="quantumGlow2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:0"/>
    </radialGradient>
    
    <radialGradient id="quantumGlow3" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.25"/>
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:0"/>
    </radialGradient>
  </defs>
  
  <!-- Dark quantum background -->
  <rect width="1920" height="1080" fill="#020617"/>
  
  <!-- Quantum field layers -->
  <g opacity="0.4">
    <!-- Large quantum fields -->
    <circle cx="300" cy="200" r="150" fill="url(#quantumGlow1)" filter="url(#glow)">
      <animate attributeName="r" values="150;180;150" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="6s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1600" cy="300" r="120" fill="url(#quantumGlow2)" filter="url(#glow)">
      <animate attributeName="r" values="120;140;120" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="7s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="800" cy="800" r="200" fill="url(#quantumGlow3)" filter="url(#glow)">
      <animate attributeName="r" values="200;230;200" dur="12s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.25;0.05;0.25" dur="9s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1400" cy="700" r="100" fill="url(#quantumGlow1)" filter="url(#glow)">
      <animate attributeName="r" values="100;130;100" dur="7s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.35;0.2" dur="5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Quantum particles -->
  <g opacity="0.6">
    <!-- Small floating particles -->
    <circle cx="150" cy="400" r="2" fill="#3b82f6" filter="url(#glow)">
      <animate attributeName="cy" values="400;350;400" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="500" cy="150" r="1.5" fill="#8b5cf6" filter="url(#glow)">
      <animate attributeName="cx" values="500;550;500" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1200" cy="500" r="2.5" fill="#06b6d4" filter="url(#glow)">
      <animate attributeName="cy" values="500;480;500" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1700" cy="600" r="1" fill="#3b82f6" filter="url(#glow)">
      <animate attributeName="cx" values="1700;1650;1700" dur="7s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="4.5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="400" cy="900" r="1.8" fill="#8b5cf6" filter="url(#glow)">
      <animate attributeName="cy" values="900;920;900" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="6s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="1000" cy="200" r="1.2" fill="#06b6d4" filter="url(#glow)">
      <animate attributeName="cx" values="1000;1030;1000" dur="9s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Quantum wave patterns -->
  <g opacity="0.3" stroke-width="0.5" fill="none" filter="url(#wave-glow)">
    <!-- Subtle wave lines -->
    <path d="M0,540 Q480,520 960,540 T1920,540" stroke="#3b82f6" opacity="0.3">
      <animate attributeName="d" values="M0,540 Q480,520 960,540 T1920,540;M0,540 Q480,560 960,540 T1920,540;M0,540 Q480,520 960,540 T1920,540" dur="15s" repeatCount="indefinite"/>
    </path>
    
    <path d="M0,300 Q640,280 1280,300 T1920,300" stroke="#8b5cf6" opacity="0.2">
      <animate attributeName="d" values="M0,300 Q640,280 1280,300 T1920,300;M0,300 Q640,320 1280,300 T1920,300;M0,300 Q640,280 1280,300 T1920,300" dur="18s" repeatCount="indefinite"/>
    </path>
    
    <path d="M0,780 Q320,760 640,780 T1920,780" stroke="#06b6d4" opacity="0.25">
      <animate attributeName="d" values="M0,780 Q320,760 640,780 T1920,780;M0,780 Q320,800 640,780 T1920,780;M0,780 Q320,760 640,780 T1920,780" dur="12s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Additional micro particles for depth -->
  <g opacity="0.4">
    <circle cx="250" cy="600" r="0.8" fill="#3b82f6">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="750" cy="350" r="0.6" fill="#8b5cf6">
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1300" cy="450" r="0.9" fill="#06b6d4">
      <animate attributeName="opacity" values="0.5;0.9;0.5" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="750" r="0.7" fill="#3b82f6">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1500" cy="150" r="0.5" fill="#8b5cf6">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>
