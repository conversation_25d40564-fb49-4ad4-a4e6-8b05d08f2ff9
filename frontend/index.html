<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="eQosystem is the emerging initiative that aims to bring minds together to drive a major scientific and technological transformation to go beyond the horizon Quantum Foundations, Quantum Software, Quantum Software AI">
    <title>eQosystem - Beyond the Horizon</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <!-- Import Vue.js from a CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-in-out',
                        'slide-up': 'slideUp 0.8s ease-in-out',
                        'slide-in-from-top': 'slideInFromTop 0.3s ease-in-out',
                        'zoom-in': 'zoomIn 0.3s ease-in-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideInFromTop: {
                            '0%': { transform: 'translateY(-10px)' },
                            '100%': { transform: 'translateY(0)' }
                        },
                        zoomIn: {
                            '0%': { transform: 'scale(0.95)' },
                            '100%': { transform: 'scale(1)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Gradient text for eQosystem logo */
        .gradient-text {
            background: linear-gradient(to right, rgb(231,107,0), rgb(231,108,231), rgb(87,142,244));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            opacity: 0.8;
        }

        /* Custom border gradients for cards */
        .border-gradient-1:hover {
            border-image: linear-gradient(to right, rgb(231,107,0), rgb(53,249,53)) 1;
        }

        .border-gradient-2:hover {
            border-image: linear-gradient(to right, rgb(53,249,53), rgb(231,108,231)) 1;
        }

        .border-gradient-3:hover {
            border-image: linear-gradient(to right, rgb(231,108,231), rgb(87,142,244)) 1;
        }

        /* Animation delays */
        .delay-300 { animation-delay: 0.3s; }
        .delay-600 { animation-delay: 0.6s; }
        .delay-900 { animation-delay: 0.9s; }

        /* Particle animation */
        .particle-canvas {
            background: linear-gradient(to bottom, #000000, #1a365d);
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            border: 1px solid rgba(255,255,255,0.1);
            color: white;
            padding: 16px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-color: rgba(34, 197, 94, 0.3);
        }

        .toast.error {
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* Form input focus styles */
        .form-input {
            background: transparent;
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s duration-300;
        }

        .form-input:hover {
            border-color: rgba(255,255,255,0.2);
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(255,255,255,0.2);
            box-shadow: none;
        }

        /* Button hover effects */
        .btn-primary {
            transition: all 0.3s ease;
        }

        .btn-primary:hover .arrow-icon {
            transform: translateX(4px);
        }

        /* Backdrop blur */
        .backdrop-blur-sm {
            backdrop-filter: blur(4px);
        }

        /* Loading spinner */
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(0,0,0,0.3);
            border-top: 2px solid #000;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <!-- Background with image -->
    <div class="fixed inset-0" style="
        background-color: #0f1518;
        background-image: url(/bg.svg);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 1;
    "></div>

    <!-- Particle Background Canvas -->
    <canvas id="particleCanvas" class="absolute top-0 left-0 w-full h-full particle-canvas"></canvas>

    <!-- Vue will mount into this container -->
    <div id="app">
        <!-- Main Content -->
        <div class="relative z-10 min-h-screen flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
            <div class="text-left w-full max-w-4xl mx-auto animate-slide-up">
                <!-- Header Section -->
                <div class="space-y-6 text-white mb-8">
                    <!-- Main Title -->
                    <p class="text-lg sm:text-xl md:text-2xl font-extralight tracking-wider text-white/90 animate-fade-in delay-300">
                        <span class="font-medium gradient-text">eQosystem</span>
                        is the emerging initiative that aims to bring minds together to drive a major scientific and technological transformation to go
                        <span class="font-medium text-white">beyond the horizon</span>
                    </p>

                    <!-- Feature Cards Grid -->
                    <div class="grid sm:grid-cols-3 gap-8 text-base sm:text-lg max-w-5xl mx-auto animate-fade-in delay-600">
                        <!-- Quantum Foundations Card -->
                        <div class="p-6 sm:p-8 rounded-[12px] border border-white/10 bg-transparent transition-all duration-700 ease-in-out group border-gradient-1"
                             @mouseenter="onCardHover($event, 1)"
                             @mouseleave="onCardLeave($event)">
                            <h3 class="font-medium mb-2 text-white/90 tracking-wide text-left">Quantum Foundations</h3>
                            <p class="font-extralight text-sm text-white/80 leading-relaxed text-left mb-2">
                                Explore and learn the quantum world up to quantum programming
                            </p>
                            <p class="text-xs text-white/50">Coming soon</p>
                        </div>

                        <!-- Quantum Software Card -->
                        <div class="p-6 sm:p-8 rounded-[12px] border border-white/10 bg-transparent transition-all duration-700 ease-in-out group border-gradient-2"
                             @mouseenter="onCardHover($event, 2)"
                             @mouseleave="onCardLeave($event)">
                            <h3 class="font-medium mb-2 text-white/90 tracking-wide text-left">Quantum Software</h3>
                            <p class="font-extralight text-sm text-white/80 leading-relaxed text-left mb-2">
                                Develop the new generation of software based on algorithms adapted to quantum computing
                            </p>
                            <p class="text-xs text-white/50">Coming soon</p>
                        </div>

                        <!-- Quantum Software AI Card -->
                        <div class="p-6 sm:p-8 rounded-[12px] border border-white/10 bg-transparent transition-all duration-700 ease-in-out group border-gradient-3"
                             @mouseenter="onCardHover($event, 3)"
                             @mouseleave="onCardLeave($event)">
                            <h3 class="font-medium mb-2 text-white/90 tracking-wide text-left">Quantum Software AI</h3>
                            <p class="font-extralight text-sm text-white/80 leading-relaxed text-left mb-2">
                                Create synergies between AI, quantum algorithms, and quantum computing power
                            </p>
                            <p class="text-xs text-white/50">Coming soon</p>
                        </div>
                    </div>
                </div>

                <!-- Subscribe Form Section -->
                <div class="w-full max-w-xl animate-slide-up delay-900">
                    <form @submit.prevent="subscribe" class="flex flex-col sm:flex-row gap-3">
                        <!-- Email Input -->
                        <div class="flex-1">
                            <input
                                v-model="email"
                                type="email"
                                placeholder="Enter your email address"
                                class="h-12 w-full form-input text-white placeholder:text-gray-400 rounded-[3px] backdrop-blur-sm px-4"
                                required
                            >
                            <div v-if="errorMessage" class="text-white/50 text-xs mt-1">{{ errorMessage }}</div>
                        </div>

                        <!-- Submit Button -->
                        <button
                            type="submit"
                            :disabled="isLoading"
                            class="btn-primary group relative h-12 px-6 bg-white hover:bg-white text-black font-light tracking-wider transition-all duration-300 border-0 flex items-center justify-center gap-2 opacity-100 w-full sm:w-auto"
                        >
                            <span v-if="!isLoading">Stay informed</span>
                            <span v-else>Subscribing...</span>
                            <div v-if="isLoading" class="spinner"></div>
                            <svg v-else class="arrow-icon w-4 h-4 transition-transform duration-300 transform translate-x-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"/>
                            </svg>
                        </button>
                    </form>

                    <!-- Form Description -->
                    <p class="mt-2 text-xs text-white/70 text-left font-extralight tracking-wider">
                        Enter your email to stay informed about the launch of<br>
                        eQosystem's first components.
                    </p>
                </div>
            </div>
        </div>

        <!-- Toast Container -->
        <div id="toastContainer"></div>
    </div>
    <script>
        // Particle Background Animation
        function initParticleBackground() {
            const canvas = document.getElementById('particleCanvas');
            const ctx = canvas.getContext('2d');

            function resizeCanvas() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }

            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            const particles = [];
            const particleCount = 100;

            // Create particles
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    radius: Math.random() * 2 + 1
                });
            }

            function drawParticle(particle) {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                ctx.fillStyle = "rgba(66, 153, 225, 0.5)";
                ctx.fill();
            }

            function connectParticles(p1, p2) {
                const distance = Math.sqrt(
                    Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2)
                );

                if (distance < 100) {
                    ctx.beginPath();
                    ctx.strokeStyle = `rgba(66, 153, 225, ${1 - distance / 100})`;
                    ctx.lineWidth = 0.5;
                    ctx.moveTo(p1.x, p1.y);
                    ctx.lineTo(p2.x, p2.y);
                    ctx.stroke();
                }
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                particles.forEach((particle, i) => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                    drawParticle(particle);

                    // Connect with nearby particles
                    for (let j = i + 1; j < particles.length; j++) {
                        connectParticles(particle, particles[j]);
                    }
                });

                requestAnimationFrame(animate);
            }

            animate();
        }

        // Toast Notification System
        function showToast(title, description, type = 'success') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            toast.innerHTML = `
                <div class="font-medium mb-1">${title}</div>
                <div class="text-sm opacity-90">${description}</div>
            `;

            toastContainer.appendChild(toast);

            // Trigger animation
            setTimeout(() => toast.classList.add('show'), 100);

            // Remove toast after 5 seconds
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toastContainer.removeChild(toast), 300);
            }, 5000);
        }

        // Vue.js Application
        const { createApp } = Vue;
        createApp({
            data() {
                return {
                    email: '',
                    errorMessage: '',
                    isLoading: false
                }
            },
            methods: {
                // Card hover effects
                onCardHover(event, cardNumber) {
                    const gradients = {
                        1: 'linear-gradient(to right, rgb(231,107,0), rgb(53,249,53)) 1',
                        2: 'linear-gradient(to right, rgb(53,249,53), rgb(231,108,231)) 1',
                        3: 'linear-gradient(to right, rgb(231,108,231), rgb(87,142,244)) 1'
                    };
                    event.target.style.borderImage = gradients[cardNumber];
                },

                onCardLeave(event) {
                    event.target.style.borderImage = 'linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.1)) 1';
                },

                // Form submission
                async subscribe() {
                    this.errorMessage = '';

                    // Validate email
                    if (!this.email || !this.email.includes('@')) {
                        this.errorMessage = '• Please enter a valid email address';
                        return;
                    }

                    this.isLoading = true;

                    try {
                        const response = await fetch('/api/subscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ email: this.email }),
                        });

                        let data;
                        try {
                            data = await response.json();
                        } catch (err) {
                            throw new Error('Unexpected server response. Please try again later.');
                        }

                        if (!response.ok) {
                            throw new Error(data.detail || data.message || 'Subscription failed');
                        }

                        // Success
                        showToast(
                            'Welcome aboard!',
                            'You will be among the first informed of the launch of the first eQosystem components.',
                            'success'
                        );

                        // Reset form
                        this.email = '';

                    } catch (error) {
                        let errorMsg = 'Something went wrong. Please try again.';

                        if (error.message.includes('Email already subscribed')) {
                            errorMsg = 'This email address is already registered to receive updates.';
                            showToast(
                                'Already registered',
                                errorMsg,
                                'error'
                            );
                        } else {
                            showToast(
                                'Error',
                                error.message,
                                'error'
                            );
                        }
                    } finally {
                        this.isLoading = false;
                    }
                }
            },
            mounted() {
                // Initialize particle background
                initParticleBackground();

                // Set body overflow
                document.body.style.overflow = 'unset';
            }
        }).mount('#app');
    </script>
</body>
</html>
