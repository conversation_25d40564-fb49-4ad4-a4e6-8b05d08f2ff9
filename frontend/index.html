<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="eQosystem - The emerging initiative bringing minds together to drive quantum transformation. Explore Quantum Foundations, Quantum Software, and Quantum Software AI.">
    <meta name="keywords" content="quantum computing, quantum software, quantum AI, eQosystem, quantum foundations">
    <meta name="author" content="eQosystem Team">
    
    <title>eQosystem - Beyond the Horizon</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    
    <!-- Vue.js 3 from CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-in-out',
                        'slide-up': 'slideUp 0.8s ease-in-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    },
                    colors: {
                        quantum: {
                            orange: '#e76b00',
                            purple: '#e76ce7', 
                            blue: '#578ef4',
                            green: '#35f935'
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        /* Quantum gradient text effect */
        .gradient-text {
            background: linear-gradient(135deg, #e76b00, #e76ce7, #578ef4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradient-shift 4s ease infinite;
        }
        
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        /* Particle canvas styling */
        .particle-canvas {
            background:
                radial-gradient(circle at 20% 30%, rgba(15, 23, 42, 0.8) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(30, 41, 59, 0.6) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(51, 65, 85, 0.4) 0%, transparent 50%),
                linear-gradient(135deg, #020617 0%, #0f172a 25%, #1e293b 50%, #0f172a 75%, #020617 100%);
        }
        
        /* Card hover effects with quantum colors */
        .quantum-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        
        .quantum-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .quantum-card-1:hover { border-color: #e76b00; box-shadow: 0 20px 40px rgba(231, 107, 0, 0.2); }
        .quantum-card-2:hover { border-color: #35f935; box-shadow: 0 20px 40px rgba(53, 249, 53, 0.2); }
        .quantum-card-3:hover { border-color: #578ef4; box-shadow: 0 20px 40px rgba(87, 142, 244, 0.2); }
        
        /* Form styling */
        .quantum-input {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .quantum-input:focus {
            outline: none;
            border-color: rgba(231, 107, 0, 0.5);
            box-shadow: 0 0 20px rgba(231, 107, 0, 0.2);
        }
        
        .quantum-button {
            background: linear-gradient(135deg, #ffffff, #f0f0f0);
            transition: all 0.3s ease;
        }
        
        .quantum-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }
        
        .toast.show { transform: translateX(0); }
        .toast.success { 
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(22, 163, 74, 0.9));
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        .toast.error { 
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        /* Loading spinner */
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(0, 0, 0, 0.3);
            border-top: 2px solid #000;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Animation delays */
        .delay-200 { animation-delay: 0.2s; }
        .delay-400 { animation-delay: 0.4s; }
        .delay-600 { animation-delay: 0.6s; }
        .delay-800 { animation-delay: 0.8s; }
    </style>
</head>
<body class="bg-gray-900 text-white overflow-x-hidden">
    <!-- Quantum Particle Background -->
    <div class="fixed inset-0 particle-canvas">
        <img src="quantum-bg.svg" alt="Quantum Background" class="w-full h-full object-cover opacity-50">
    </div>
    
    <!-- Vue.js Application Container -->
    <div id="app">
        <!-- Main Content Container -->
        <div class="relative z-10 min-h-screen flex flex-col items-center justify-center px-4 py-12">
            
            <!-- Header Section -->
            <header class="max-w-7xl mx-auto mb-16 animate-fade-in">
                <!-- Main Title -->
                <div class="text-left">
                    <h1 class="text-2xl md:text-3xl lg:text-4xl font-light leading-relaxed mb-8 text-white/90">
                        <div class="gradient-text font-medium text-3xl md:text-4xl lg:text-5xl mb-2">eQosystem</div>
                        <div class="text-lg md:text-xl lg:text-2xl">
                            is the emerging initiative that aims to bring minds together<br>
                            to drive a major scientific and technological transformation<br>
                            to go <span class="font-medium text-white">beyond the horizon</span>
                        </div>
                    </h1>
                </div>
            </header>
            
            <!-- Quantum Domains Cards -->
            <section class="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto mb-16 animate-slide-up delay-200">
                
                <!-- Quantum Foundations Card -->
                <article class="quantum-card quantum-card-1 p-8 rounded-2xl bg-black/20 backdrop-blur-sm">
                    <h2 class="text-xl font-medium mb-4 text-white/90">Quantum Foundations</h2>
                    <p class="text-white/70 leading-relaxed mb-4">
                        Explore and learn the quantum world up to quantum programming.
                        Build your understanding from quantum mechanics to practical applications.
                    </p>
                    <span class="text-sm text-quantum-orange/80 font-medium">Coming Soon</span>
                </article>
                
                <!-- Quantum Software Card -->
                <article class="quantum-card quantum-card-2 p-8 rounded-2xl bg-black/20 backdrop-blur-sm">
                    <h2 class="text-xl font-medium mb-4 text-white/90">Quantum Software</h2>
                    <p class="text-white/70 leading-relaxed mb-4">
                        Develop the new generation of software based on algorithms adapted to quantum computing.
                        Create solutions that harness quantum advantages.
                    </p>
                    <span class="text-sm text-quantum-green/80 font-medium">Coming Soon</span>
                </article>
                
                <!-- Quantum Software AI Card -->
                <article class="quantum-card quantum-card-3 p-8 rounded-2xl bg-black/20 backdrop-blur-sm">
                    <h2 class="text-xl font-medium mb-4 text-white/90">Quantum Software AI</h2>
                    <p class="text-white/70 leading-relaxed mb-4">
                        Create synergies between AI, quantum algorithms, and quantum computing power.
                        Pioneer the future of intelligent quantum systems.
                    </p>
                    <span class="text-sm text-quantum-blue/80 font-medium">Coming Soon</span>
                </article>
                
            </section>
            
            <!-- Newsletter Subscription Section -->
            <section class="max-w-7xl mx-auto animate-slide-up delay-400">
                <form @submit.prevent="subscribeToNewsletter" class="flex flex-col sm:flex-row gap-4 mb-4">

                    <!-- Email Input -->
                    <div class="flex-1">
                        <input
                            v-model="email"
                            type="email"
                            placeholder="Enter your email address"
                            class="quantum-input w-full h-14 px-6 rounded-xl text-white placeholder-white/50"
                            :disabled="isLoading"
                            required
                        >
                        <p v-if="errorMessage" class="text-red-400 text-sm mt-2">{{ errorMessage }}</p>
                    </div>
                    
                    <!-- Subscribe Button -->
                    <button
                        type="submit"
                        :disabled="isLoading"
                        class="quantum-button h-14 px-8 rounded-xl text-black font-medium flex items-center justify-center gap-3 min-w-[160px]"
                    >
                        <span v-if="!isLoading">Stay Informed</span>
                        <span v-else>Subscribing...</span>
                        
                        <div v-if="isLoading" class="spinner"></div>
                        <svg v-else class="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" 
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"/>
                        </svg>
                    </button>
                    
                </form>
                
                <!-- Form Description -->
                <p class="text-left text-white/60 text-sm leading-relaxed">
                    Enter your email to stay informed about the launch of eQosystem's first components and quantum innovations.
                </p>
            </section>
            
        </div>
    </div>
    
    <!-- Toast Notification Container -->
    <div id="toastContainer"></div>

    <!-- Vue.js Application Script -->
    <script>


        // =============================================================================
        // TOAST NOTIFICATION SYSTEM
        // =============================================================================

        class ToastManager {
            constructor() {
                this.container = document.getElementById('toastContainer');
            }

            show(title, message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                toast.innerHTML = `
                    <div class="font-semibold mb-1">${title}</div>
                    <div class="text-sm opacity-90">${message}</div>
                `;

                this.container.appendChild(toast);

                // Trigger show animation
                setTimeout(() => toast.classList.add('show'), 100);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (this.container.contains(toast)) {
                            this.container.removeChild(toast);
                        }
                    }, 300);
                }, 5000);
            }
        }

        // =============================================================================
        // VUE.JS APPLICATION
        // =============================================================================

        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    email: '',
                    isLoading: false,
                    errorMessage: '',
                    toastManager: null
                }
            },

            methods: {
                async subscribeToNewsletter() {
                    // Reset error state
                    this.errorMessage = '';

                    // Validate email format
                    if (!this.email || !this.isValidEmail(this.email)) {
                        this.errorMessage = 'Please enter a valid email address';
                        return;
                    }

                    this.isLoading = true;

                    try {
                        const response = await fetch('/api/subscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ email: this.email })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            // Success
                            this.toastManager.show(
                                'Welcome to eQosystem! 🚀',
                                'You will be among the first to know about our quantum innovations.',
                                'success'
                            );
                            this.email = ''; // Clear form
                        } else {
                            // API error
                            throw new Error(data.detail || 'Subscription failed');
                        }

                    } catch (error) {
                        // Handle different error types
                        if (error.message.includes('already subscribed')) {
                            this.toastManager.show(
                                'Already Subscribed',
                                'This email is already registered for updates.',
                                'error'
                            );
                        } else {
                            this.toastManager.show(
                                'Subscription Error',
                                'Something went wrong. Please try again later.',
                                'error'
                            );
                        }

                        console.error('Subscription error:', error);
                    } finally {
                        this.isLoading = false;
                    }
                },

                isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                }
            },

            mounted() {
                // Initialize toast manager
                this.toastManager = new ToastManager();

                console.log('🌟 eQosystem Landing Page Initialized');
            }
        }).mount('#app');
    </script>
</body>
</html>
