<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>eQosystem</title>
  <!-- Import Vue.js from a CDN -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <style>
    /* Basic styling for the landing page */
    body { font-family: Arial, sans-serif; background:#0f1518; color:white; text-align:center; margin:0; }
    .container { max-width:600px; margin:4rem auto; padding:1rem; }
    input[type="email"] { padding:0.5rem; width:70%; border-radius:4px; border:1px solid #ccc; }
    button { padding:0.5rem 1rem; margin-left:0.5rem; border:none; border-radius:4px; cursor:pointer; }
    .message { margin-top:1rem; }
  </style>
</head>
<body>
  <!-- Vue will mount into this container -->
  <div id="app" class="container">
    <h1>eQosystem</h1>
    <p>Beyond the horizon</p>
    <form @submit.prevent="subscribe">
      <input v-model="email" type="email" placeholder="Enter your email" required />
      <button type="submit">Subscribe</button>
    </form>
    <p class="message">{{ message }}</p>
  </div>
  <script>
    // Simple Vue application used only for the subscription form
    const { createApp } = Vue
    createApp({
      data() {
        return { email: '', message: '' }
      },
      methods: {
        // Send the email address to the backend and display the result
        async subscribe() {
          this.message = ''
          try {
            const res = await fetch('/api/subscribe', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email: this.email })
            })
            const data = await res.json()
            if (!res.ok) throw new Error(data.message || 'Error')
            this.message = 'Thank you for subscribing!'
            this.email = ''
          } catch (err) {
            this.message = err.message
          }
        }
      }
    }).mount('#app')
  </script>
</body>
</html>
