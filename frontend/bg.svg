<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080">
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#1a365d;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#0f1518;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
    </radialGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>
  
  <!-- Subtle geometric patterns -->
  <g opacity="0.1">
    <!-- Large circle -->
    <circle cx="1600" cy="200" r="300" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <circle cx="1600" cy="200" r="250" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
    
    <!-- Small circles -->
    <circle cx="300" cy="800" r="150" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1"/>
    <circle cx="1200" cy="900" r="100" fill="none" stroke="rgba(255,255,255,0.06)" stroke-width="1"/>
    
    <!-- Lines -->
    <line x1="0" y1="400" x2="1920" y2="600" stroke="rgba(255,255,255,0.03)" stroke-width="1"/>
    <line x1="0" y1="700" x2="1920" y2="500" stroke="rgba(255,255,255,0.03)" stroke-width="1"/>
  </g>
  
  <!-- Quantum-inspired dots -->
  <g opacity="0.3" filter="url(#glow)">
    <circle cx="1400" cy="300" r="2" fill="rgba(87,142,244,0.6)"/>
    <circle cx="500" cy="600" r="1.5" fill="rgba(231,108,231,0.6)"/>
    <circle cx="800" cy="200" r="1" fill="rgba(231,107,0,0.6)"/>
    <circle cx="1100" cy="700" r="2.5" fill="rgba(53,249,53,0.4)"/>
    <circle cx="200" cy="400" r="1.5" fill="rgba(87,142,244,0.5)"/>
    <circle cx="1600" cy="800" r="1" fill="rgba(231,108,231,0.5)"/>
  </g>
</svg>
