<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <!-- Quantum gradient definition -->
    <linearGradient id="quantumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e76b00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e76ce7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#578ef4;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="50" cy="50" r="45" fill="url(#quantumGradient)" opacity="0.9"/>
  
  <!-- Inner quantum symbol -->
  <g filter="url(#glow)">
    <!-- Quantum 'e' -->
    <path d="M35 35 Q45 25 55 35 Q45 45 35 35" fill="white" opacity="0.9"/>
    <!-- Quantum 'Q' -->
    <circle cx="50" cy="60" r="12" fill="none" stroke="white" stroke-width="3" opacity="0.9"/>
    <line x1="58" y1="68" x2="65" y2="75" stroke="white" stroke-width="3" opacity="0.9"/>
  </g>
  
  <!-- Quantum dots -->
  <circle cx="25" cy="25" r="2" fill="white" opacity="0.7"/>
  <circle cx="75" cy="25" r="1.5" fill="white" opacity="0.6"/>
  <circle cx="25" cy="75" r="1" fill="white" opacity="0.5"/>
  <circle cx="75" cy="75" r="2" fill="white" opacity="0.8"/>
</svg>
